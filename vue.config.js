const path = require('path')
const CompressionPlugin = require('compression-webpack-plugin')

function resolve(dir) {
  return path.join(__dirname, dir)
}

// vue.config.js
module.exports = {
  runtimeCompiler: true,
  //devtool调试用，生产可以去掉
  configureWebpack: {
    devtool: 'source-map',
  },
  /*
    Vue-cli3:
    Crashed when using Webpack `import()` #2463
    https://github.com/vuejs/vue-cli/issues/2463
   */
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,
  // 多入口配置
  // pages: {
  //   index: {
  //     entry: 'src/main.js',
  //     template: 'public/index.html',
  //     filename: 'index.html',
  //   }
  // },
  //打包app时放开该配置
  //publicPath:'./',
  configureWebpack: (config) => {
    //生产环境取消 console.log
    if (process.env.NODE_ENV === 'production') {
      config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true
    }
  },
  chainWebpack: (config) => {
    config.resolve.alias
      .set('@$', resolve('src'))
      .set('@api', resolve('src/api'))
      .set('@assets', resolve('src/assets'))
      .set('@comp', resolve('src/components'))
      .set('@views', resolve('src/views'))

    //生产环境，开启js\css压缩
    if (process.env.NODE_ENV === 'production') {
      config.plugin('compressionPlugin').use(
        new CompressionPlugin({
          test: /\.(js|css|less)$/, // 匹配文件名
          threshold: 10240, // 对超过10k的数据压缩
          deleteOriginalAssets: false, // 不删除源文件
        })
      )
    }

    // 配置 webpack 识别 markdown 为普通的文件
    config.module.rule('markdown').test(/\.md$/).use().loader('file-loader').end()

    // 编译vxe-table包里的es6代码，解决IE11兼容问题
    config.module
      .rule('vxe')
      .test(/\.js$/)
      .include.add(resolve('node_modules/vxe-table'))
      .add(resolve('node_modules/vxe-table-plugin-antd'))
      .end()
      .use()
      .loader('babel-loader')
      .end()
  },
  configureWebpack: (config) => {
    if (process.env.NODE_ENV === 'production') {
      // 为生产环境修改配置...
      config.mode = 'production'
      config['performance'] = {
        //打包文件大小配置
        maxEntrypointSize: 15000000,
        maxAssetSize: 30000000,
      }
    }
  },

  css: {
    loaderOptions: {
      less: {
        modifyVars: {
          /* less 变量覆盖，用于自定义 ant design 主题 */
          'primary-color': '#1890FF',
          'link-color': '#1890FF',
          'border-radius-base': '4px',
        },
        javascriptEnabled: true,
      },
    },
  },

  devServer: {
    port: 9888,
    // public: require('os').networkInterfaces()[Object.keys(require('os').networkInterfaces())[0]][1].address + ':9888',
    proxy: {
      /* '/api': {
        target: 'https://mock.ihx.me/mock/5baf3052f7da7e07e04a5116/antd-pro', //mock API接口系统
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '/jky-boot': ''  //默认所有请求都加了jky-boot前缀，需要去掉
        }
      },*/
      '/jky-boot': {
        target: 'https://docgd.dgjky.com/api', //代理到远程服务器
        ws: false,
        changeOrigin: true,
        secure: true, // 如果是https，需要设置为true
        pathRewrite: {
          '^/jky-boot': '/jky-boot' // 保持路径不变
        },
        // 添加自定义头部来处理CORS问题
        headers: {
          'Origin': 'http://localhost:9888'
        },
        // 拦截响应，修复CORS头问题
        onProxyRes: function (proxyRes, req, res) {
          // 强制设置正确的CORS头，避免重复值问题
          proxyRes.headers['access-control-allow-origin'] = 'http://localhost:9888';
          proxyRes.headers['access-control-allow-credentials'] = 'true';
          proxyRes.headers['access-control-allow-methods'] = 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS';
          proxyRes.headers['access-control-allow-headers'] = 'Content-Type, Authorization, X-Requested-With';

          // 删除可能冲突的头
          delete proxyRes.headers['access-control-allow-origin-duplicate'];
        }
      },
    },
  },

  lintOnSave: undefined,
}
