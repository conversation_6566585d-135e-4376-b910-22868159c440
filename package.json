{"name": "vue-antd-jky", "version": "1.1.0", "description": "建筑工地档案管理平台", "author": "jky", "license": "MIT", "homepage": "http//www.dgjky.com", "scripts": {"pre": "cnpm install || yarn --registry https://registry.npm.taobao.org || npm install --registry https://registry.npm.taobao.org ", "serve": "vue-cli-service serve", "build:test": "vue-cli-service build --mode test", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@antv/data-set": "^0.11.4", "@babel/parser": "^7.7.4", "@dcloudio/uni-webview-js": "^0.0.3", "@fullcalendar/core": "^5.11.3", "@fullcalendar/daygrid": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@fullcalendar/list": "^5.11.3", "@fullcalendar/moment": "^5.11.3", "@fullcalendar/resource-timeline": "^5.11.3", "@fullcalendar/scrollgrid": "^5.11.3", "@fullcalendar/timegrid": "^5.11.3", "@fullcalendar/vue": "^5.11.2", "@jeecg/antd-online-mini": "3.1.0-beta", "@riophae/vue-treeselect": "0.4.0", "@smallwei/avue": "^2.8.23", "@tinymce/tinymce-vue": "2.1.0", "@toast-ui/editor": "^2.1.2", "@vue-office/pdf": "^1.6.5", "ant-design-vue": "^1.7.2", "axios": "^0.18.0", "beautifier": "^0.1.7", "bpmn-js-sketchy": "^0.5.3", "bpmn-js-task-resize": "^1.2.0", "bpmn-js-token-simulation": "^0.10.0", "china-area-data": "^5.0.1", "clipboard": "^2.0.10", "codemirror": "^5.46.0", "core-js": "3.8.1", "cron-parser": "^2.10.0", "crypto-js": "^4.1.1", "dayjs": "^1.8.0", "dhtmlx-gantt": "^8.0.3", "diagram-js-minimap": "^2.0.4", "dom-align": "1.12.0", "echarts": "^4.9.0", "element-ui": "2.15.6", "enquire.js": "^2.1.6", "file-saver": "2.0.4", "form-gen-parser": "^1.0.3", "highlight.js": "^10.5.0", "jquery": "^3.6.0", "js-cookie": "^2.2.0", "js-pinyin": "^0.1.9", "jsbarcode": "^3.11.4", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "mathjs": "^9.5.1", "mavon-editor": "^2.10.1", "md5": "^2.2.1", "miment": "^0.0.9", "min-dash": "^3.5.2", "normalize.css": "7.0.0", "nprogress": "^0.2.0", "qrcodejs2": "0.0.2", "quill": "^1.3.7", "sass": "1.32.0", "sass-loader": "10.1.0", "solarday2lunarday": "^1.12.1", "spark-md5": "^3.0.1", "tinymce": "5.4.1", "v-chart": "^1.0.0", "v-charts": "^1.17.10", "v-click-outside-x": "^4.1.3", "view-design": "^4.2.0", "view-ui-plus": "^1.3.1", "viser-vue": "^2.4.8", "voca": "^1.4.0", "vue": "^2.6.10", "vue-area-linkage": "^5.1.0", "vue-barcode": "^1.3.0", "vue-clipboards": "^1.2.4", "vue-codemirror": "^4.0.6", "vue-color": "^2.8.1", "vue-cropper": "^0.5.4", "vue-demi": "^0.14.6", "vue-drag-resize": "^1.5.4", "vue-echarts": "^5.0.0-beta.0", "vue-i18n": "^8.7.0", "vue-json-editor": "1.4.3", "vue-loader": "^15.7.0", "vue-ls": "^3.2.0", "vue-photo-preview": "^1.1.3", "vue-print-nb-jeecg": "^1.0.11", "vue-quill-editor": "^3.0.6", "vue-router": "^3.0.1", "vue-ruler-tool": "^1.2.4", "vue-simple-uploader": "^0.7.6", "vue-splitpane": "^1.0.4", "vue-superslide": "^0.1.1", "vuedraggable": "^2.20.0", "vuescroll": "^4.6.24", "vuex": "^3.1.0", "vuex-persistedstate": "^4.1.0", "vxe-table": "2.9.13", "vxe-table-plugin-antd": "1.8.10", "wangeditor": "^3.1.1", "weixin-js-sdk": "^1.6.0", "workflow-bpmn-modeler-jeecgboot": "^0.1.3", "x2js": "^3.4.2", "xe-utils": "2.4.8", "xml-js": "^1.6.11"}, "devDependencies": {"@babel/polyfill": "^7.2.5", "@vue/cli-plugin-babel": "^4.5.0", "@vue/cli-plugin-eslint": "^4.5.0", "@vue/cli-service": "^4.5.0", "@vue/compiler-sfc": "^3.0.1", "@vue/eslint-config-prettier": "^5.0.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.1.0", "bpmn-js": "^8.8.3", "bpmn-js-properties-panel": "^0.37.2", "camunda-bpmn-moddle": "^4.4.1", "compression-webpack-plugin": "^6.1.1", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-vue": "^6.2.2", "fs-extra": "^8.1.0", "html-webpack-plugin": "^4.2.0", "js-md5": "^0.7.3", "less": "^3.9.0", "less-loader": "^4.1.0", "path-to-regexp": "2.4.0", "style-resources-loader": "^1.2.1", "stylus": "^0.54.7", "stylus-loader": "^3.0.2", "svg-sprite-loader": "5.1.1", "video.js": "^7.18.1", "vue-template-compiler": "^2.6.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/strongly-recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"generator-star-spacing": "off", "no-mixed-operators": 0, "vue/max-attributes-per-line": [2, {"singleline": 5, "multiline": {"max": 1, "allowFirstLine": false}}], "vue/attribute-hyphenation": 0, "vue/html-self-closing": 0, "vue/component-name-in-template-casing": 0, "vue/html-closing-bracket-spacing": 0, "vue/singleline-html-element-content-newline": 0, "vue/no-unused-components": 0, "vue/multiline-html-element-content-newline": 0, "vue/no-use-v-if-with-v-for": 0, "vue/html-closing-bracket-newline": 0, "vue/no-parsing-error": 0, "no-tabs": 0, "indent": ["off", 2], "no-console": 0, "space-before-function-paren": 0}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}