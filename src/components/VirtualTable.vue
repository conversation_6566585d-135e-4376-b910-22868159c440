<template>
  <div class="virtual-table-container">
    <!-- 使用 vxe-table 的虚拟滚动功能 -->
    <vxe-table
      ref="virtualTable"
      :data="dataSource"
      :height="tableHeight"
      :scroll-y="{ enabled: true, gt: 100 }"
      :scroll-x="{ enabled: true }"
      :expand-config="expandConfig"
      @toggle-tree-expand="handleTreeExpand"
    >
      <vxe-column type="expand" width="50">
        <template #content="{ row }">
          <div class="expand-content">
            <!-- 展开内容的懒加载 -->
            <a-table
              v-if="expandedData[row.id]"
              :columns="expandColumns"
              :dataSource="expandedData[row.id]"
              :pagination="false"
              size="small"
            />
            <a-spin v-else-if="loadingExpanded[row.id]" />
            <div v-else @click="loadExpandData(row)">
              点击加载详细数据
            </div>
          </div>
        </template>
      </vxe-column>
      
      <vxe-column
        v-for="column in columns"
        :key="column.dataIndex"
        :field="column.dataIndex"
        :title="column.title"
        :width="column.width"
      />
    </vxe-table>
  </div>
</template>

<script>
export default {
  name: 'VirtualTable',
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
    columns: {
      type: Array,
      default: () => []
    },
    expandColumns: {
      type: Array,
      default: () => []
    },
    tableHeight: {
      type: [String, Number],
      default: 400
    }
  },
  data() {
    return {
      expandConfig: {
        lazy: true,
        iconOpen: 'fa fa-minus-square-o',
        iconClose: 'fa fa-plus-square-o'
      },
      expandedData: {}, // 存储展开行的数据
      loadingExpanded: {} // 存储展开行的加载状态
    }
  },
  methods: {
    // 懒加载展开数据
    async loadExpandData(row) {
      if (this.expandedData[row.id]) return;
      
      this.$set(this.loadingExpanded, row.id, true);
      
      try {
        // 模拟异步加载数据
        const data = await this.fetchExpandData(row.id);
        this.$set(this.expandedData, row.id, data);
      } catch (error) {
        console.error('加载展开数据失败:', error);
      } finally {
        this.$set(this.loadingExpanded, row.id, false);
      }
    },
    
    // 获取展开数据的API调用
    async fetchExpandData(rowId) {
      // 这里替换为实际的API调用
      return new Promise(resolve => {
        setTimeout(() => {
          resolve([
            { id: 1, name: '子数据1', value: '值1' },
            { id: 2, name: '子数据2', value: '值2' }
          ]);
        }, 500);
      });
    },
    
    handleTreeExpand({ row, expanded }) {
      if (expanded) {
        this.loadExpandData(row);
      }
    }
  }
}
</script>

<style scoped>
.virtual-table-container {
  width: 100%;
  height: 100%;
}

.expand-content {
  padding: 10px;
  background-color: #f5f5f5;
}
</style>
