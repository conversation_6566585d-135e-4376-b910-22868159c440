<template>
  <j-modal
    title="选择组织机构"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    wrapClassName="j-depart-select-modal"
    cancelText="关闭">
    <a-form-model ref="form" :model="model" :rules="validatorRules">
      <a-form-model-item label="企业类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="entType">
        <j-dict-select-tag type="list" v-model="model.entType" title="企业类型" dictCode="dgzjjsp_ent_type" placeholder="请选择企业类型"/>
      </a-form-model-item>
      <a-form-model-item label="企业名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="organizationName" v-if='isOrganization'>
        <template id='userOrganizationList'>
          <a-select multiple v-model='model.organizationName'
                    :show-search="true"
                    :filter-option="true"
                    :allowClear="true"
                    placeholder="请输入企业名称至少输入2个字或以上"
                    @search="handleSearch"
                    @blur="handleBlur"
                    @change="handleChange">
            <a-select-option v-for='organization in organizationList' @click='selectOrganizationId(organization)' :value='organization.entName' open='true'>{{ organization.entName }}</a-select-option>
          </a-select>
        </template>
      </a-form-model-item>
    </a-form-model>
  </j-modal>
</template>

<script>
import { ajaxGetDictItems, getDictText, queryEntCollect } from '@api/api'
  import JDictSelectTag from '@comp/dict/JDictSelectTag.vue'
import async from 'async'

  export default {
    name: 'JSelectOrganizationModal',
    components:{
      JDictSelectTag
    },
    props:['modalWidth', 'text'],
    data(){
      return {
        visible:false,
        isSelect:false,
        confirmLoading:false,
        organizationList:[],
        dictCode:"dgzjjsp_ent_type",
        selectMultipleType:"list_multi",
        isOrganization:false,
        entTypeRoleDict:"",
        model:{
          entType:"",
          organizationName:"",
          organizationId:""
        },
        validatorRules:{
          entType:[{
            required: true,
            message: '请选择企业类型!'
          }],
          organizationName:[{
            required: true,
            message: '请输入企业名称!'
          }]
        },
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        }
      }
    },
    created(){
      // this.loadDepart();
      this.model.organizationName = ""
      this.model.organizationId = ""
      this.organizationList = []
      this.model.entType = ""
      ajaxGetDictItems("dgzjjsp_entType_role", null).then((res) => {
        if (res.success) {
          this.entTypeRoleDict = res.result;
        }
      })
    },
    watch:{
      'model.organizationName'(newVal,oldVal) {
        if (newVal != null) {
          queryEntCollect({entName:newVal,entType:this.model.entType}).then((res) => {
            if (res.success) {
              this.organizationList = res.result.list
            }
          })
        }
      },
      'model.entType'(newVal,oldVal) {
        if (newVal != null && newVal != '') {
          this.isOrganization = true
          queryEntCollect({entType:this.model.entType}).then((res) => {
            if (res.success) {
              this.organizationList = res.result.list
            }
          })
          // 替换类型后，清除企业名称
          if (newVal != oldVal) {
            this.organizationList = []
            this.model.organizationName = ""
            this.model.organizationId = ""
          }
        }
      },
      deep:true
    },
    methods:{
      selectOrganizationId(organization){
        this.model.organizationName = organization.entName
        this.model.organizationId = organization.corporationRegCode
        this.isSelect = true
      },
      show(){
        this.visible=true
        this.isSelect = false
        this.isOrganization = false
        this.model.organizationId = ''
        this.model.organizationName = ''
        this.organizationList = []
        this.model.entType = ""
      },
      handleSubmit(){
        if (this.isSelect) {
          this.visible=false
          this.isSelect = false
          let roleId = ""
          for (const roleIdKey in this.entTypeRoleDict) {
            if (this.entTypeRoleDict[roleIdKey].value == this.model.entType) {
              roleId = this.entTypeRoleDict[roleIdKey].text
            }
          }
          const res = {
            organizationId: this.model.organizationId,
            organizationName: this.model.organizationName,
            entType: this.model.entType,
            roleId: roleId
          }
          this.$emit('ok',res)
        } else {
          if (this.model.entType != null && this.model.entType != '') {
            this.$message.error("请选择组织机构")
          } else {
            this.$message.error("请选择企业类型")
          }
        }
      },
      handleCancel(){
        this.handleClear()
      },
      handleClear(){
        this.visible=false
        this.isSelect = false
        this.model.organizationId=''
        this.model.organizationName=''
        this.organizationList = []
        this.model.entType = ""
        const res = {
          organizationId: this.model.organizationId,
          organizationName: this.model.organizationName,
          entType: this.model.entType
        }
        this.$emit('ok',res)
      },
      handleSearch(value){
        this.handleChange(value)
      },

      handleChange(value){
        this.model.organizationName = (value != null && value != '') ? value : undefined
      },

      handleBlur(value){
        this.model.organizationName = value
      }
    }
  }

</script>

<style lang="less" scoped>
  // 限制机构选择树高度，避免机构太多时点击确定不便
  .my-dept-select-tree{
    height:350px;

    &.fullscreen{
      height: calc(100vh - 250px);
    }
    overflow-y: scroll;
  }
  .drawer-bootom-button {
    position: absolute;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    left: 0;
    background: #fff;
    border-radius: 0 0 2px 2px;
  }
</style>