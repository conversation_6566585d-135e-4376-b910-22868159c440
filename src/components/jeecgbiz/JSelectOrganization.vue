<template>
  <div class="components-input-demo-presuffix">
    <!---->
    <a-input @click="openModal" placeholder="请点击选择组织机构" v-model="textVals" readOnly :disabled="disabled">
      <a-icon slot="prefix" type="cluster" title="组织机构选择控件"/>
<!--      <a-icon v-if="storeVals" slot="suffix" type="close-circle" @click="handleEmpty" title="清空"/>-->
    </a-input>

    <j-select-organization-modal
      ref="innerOrganizationSelectModal"
      :modal-width="modalWidth"
      :text="textVals"
      @ok="handleOK"
      @initComp="initComp"/>
  </div>
</template>

<script>
  import JSelectDepartModal from './modal/JSelectDepartModal'
  import { underLinetoHump } from '@/components/_util/StringUtil'
  import JSelectOrganizationModal from '@comp/jeecgbiz/modal/JSelectOrganizationModal.vue'
  export default {
    name: 'JSelectOrganization',
    components:{
      JSelectOrganizationModal,
      // JSelectDepartModal
    },
    props:{
      modalWidth:{
        type:Number,
        default:500,
        required:false
      },
      value:{
        type:String,
        required:false
      },
      disabled:{
        type: Boolean,
        required: false,
        default: false
      },
      // 自定义返回字段，默认返回 id
      customReturnField: {
        type: String,
        default: ''
      },
      backOrganization: {
        type: Boolean,
        default: false,
        required: false
      },
      // 存储字段 [key field]
      store: {
        type: String,
        default: 'id',
        required: false
      },
      // 显示字段 [label field]
      text: {
        type: String,
        default: 'departName',
        required: false
      }
      
    },
    data(){
      return {
        visible:false,
        confirmLoading:false,
        storeVals: '', //[key values]
        textVals: '', //[label values]
        organizationId:''
      }
    },
    computed:{

    },
    mounted(){
      this.textVals = this.$props.value
    },
    watch:{
      value(val){
        this.textVals = val
      }
    },
    methods:{
      initComp(textVals){
        this.textVals = textVals
      },
      //返回选中的机构信息
      backOrganizationInfo(organization){
        this.$emit('back', organization)
      },
      openModal(){
        this.$refs.innerOrganizationSelectModal.show()
      },
      handleOK(organization) {
        this.textVals = organization.organizationId;
        const res ={
          organizationId:this.textVals,
          organizationName:organization.organizationName,
          entType: organization.entType,
          roleId: organization.roleId
        }
        this.backOrganizationInfo(res)
      },
      getDepartNames(){
        return this.departNames
      },
      handleEmpty(){
        const res = {
          organizationId: '',
          organizationName: ''
        }
        this.handleOK(res)
      }
    },
    model: {
      prop: 'value',
      event: 'change'
    }
  }
</script>

<style scoped>
  .components-input-demo-presuffix .anticon-close-circle {
    cursor: pointer;
    color: #ccc;
    transition: color 0.3s;
    font-size: 12px;
  }
  .components-input-demo-presuffix .anticon-close-circle:hover {
    color: #f5222d;
  }
  .components-input-demo-presuffix .anticon-close-circle:active {
    color: #666;
  }
</style>