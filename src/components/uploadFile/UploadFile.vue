<template>
  <div>
    <a-upload
      name="file"
      :multiple="true"
      :showUploadList="true"
      :before-upload="beforeUpload"
      :remove="handleRemove"
      :default-file-list="this.initFileList"
      @preview="handleDownload"
      :disabled="disabled"
      accept=".docx,.doc,.pdf,.jpg,.jpeg,.xlsx,.xls,.png"
    >
      <a-button v-if="showBut">
        <a-icon type="upload"/>
        附件上传
      </a-button>
    </a-upload>
  </div>
</template>


<script>
// import {downloadMinioFile} from "@api/api";
import { deleteAction, downloadFile, postAction } from '@api/manage'

export default {
  name: 'UploadFile',
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
    defaultFileList: {
      type: Array,
      default: ()=>[],
      required: false
    },
    showBut: {
      type: Boolean,
      default: false,
      required: false
    },
  },
  data() {
    return {
      url: {
      },
      fileList: [],
      delFileList: [],
      initFileList: []
    }
  },
  computed: {

  },
  watch:{
    defaultFileList(newVal,oldVal) {
      let count = 0;
      if (newVal) {
        this.initFileList.splice(0,this.initFileList.length)
        newVal.forEach(it => {
          this.initFileList.push({
            id: it.id,
            uid: count++,
            name: it.fileName,
            status: 'done',
            response: 'Server Error 500', // custom error message to show
            url: 'javascript:',
          })
        })
      }
    }
  },
  created() {
    if (this.$props.defaultFileList) {
      let count = 0;
      this.$props.defaultFileList.forEach(it => {
        this.initFileList.push({
          id: it.id,
          uid: count++,
          name: it.fileName,
          status: 'done',
          response: 'Server Error 500', // custom error message to show
          url: 'javascript:',
        })
      })
    }
  },
  methods: {
    beforeUpload(file) {
      if (file) {
        let name = file.name
        let type = name.substring(name.lastIndexOf(".") + 1)
        if ("docx,doc,pdf,jpg,jpeg,xlsx,xls,png".indexOf(type) != -1) {
          this.fileList = [...this.fileList, file];
          return false;
        } else {
          this.$message.error("上传文件格式错误")
          return true
        }
      }
    },
    handleRemove(file) {
      if (this.fileList.length > 0){
        for (const fileIndex in this.fileList) {
           if (file.uid == this.fileList[fileIndex].uid) {
             const newFileList = this.fileList.slice();
             newFileList.splice(fileIndex, 1);
             this.fileList = newFileList;
             this.delFileList.push(file.id)
             break
           }
        }
      }
      if (this.initFileList.length > 0) {
        for (const fileIndex in this.initFileList) {
          if (file.uid == this.initFileList[fileIndex].uid) {
            const newFileList = this.initFileList.slice();
            newFileList.splice(fileIndex, 1);
            this.initFileList = newFileList;
            this.delFileList.push(file.id)
            break
          }
        }
      }
    },
    handleDownload(file) {
      if (file.status && file.status === "done") {
        downloadFile('/regform/download/' + file.id, file.name, {})
      }
    },
    handleUpload(url,dir,addParam) {
      let paramId =  addParam.dataId
      const uploadUrl = dir + "/" + addParam.dataId
      this.uploading = true;
      if (this.fileList) {
        this.fileList.forEach(file => {
          if (file) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('biz', uploadUrl);
            postAction(url + '/upload/'  + addParam.dataId + '/' + addParam.captcha + '/' + addParam.checkKey, formData).then((res) => {
              if (res.success) {
                this.fileList = []
                this.uploading = false;
                this.$message.success('上传成功');
              } else {
                this.uploading = false;
                this.$message.error(res.message);
              }
            })
          }
        });
      }
      if (this.delFileList) {
        this.delFileList.forEach(id => {
          // 删除文件
          const delParam = {}
          delParam.fileId = id
          delParam.captcha = addParam.captcha
          delParam.checkKey = addParam.checkKey
          deleteAction(url + "/delFile", delParam)
        })
      }
    }
  }
}

</script>

<style scoped>

</style>