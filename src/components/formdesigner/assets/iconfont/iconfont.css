@font-face {
  font-family: "iconfont"; /* Project id 1782818 */
  src: url('iconfont.woff2?t=1677832299229') format('woff2'),
       url('iconfont.woff?t=1677832299229') format('woff'),
       url('iconfont.ttf?t=1677832299229') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-shanchulie:before {
  content: "\e6ef";
}

.icon-shanchuhang:before {
  content: "\e6f0";
}

.icon-table_layout:before {
  content: "\e7de";
}

.icon-chaifenhang:before {
  content: "\e60a";
}

.icon-chaifenlie:before {
  content: "\e60b";
}

.icon-chaifen:before {
  content: "\e60d";
}

.icon-zhuijiahang:before {
  content: "\e6ce";
}

.icon-zhuijialie:before {
  content: "\e6cf";
}

.icon-charuhang:before {
  content: "\e8a2";
}

.icon-charulie:before {
  content: "\e8a4";
}

.icon-shangxiahebing:before {
  content: "\e66a";
}

.icon-zuoyouhebing:before {
  content: "\e66b";
}

.icon-treeselect:before {
  content: "\e609";
}

.icon-barcode2:before {
  content: "\e66e";
}

.icon-barcode:before {
  content: "\e612";
}

.icon-dialog:before {
  content: "\e692";
}

.icon-address:before {
  content: "\e6b5";
}

.icon-cascader:before {
  content: "\e697";
}

.icon-colorpicker:before {
  content: "\e666";
}

.icon-shengshiqu:before {
  content: "\e75f";
}

.icon-text:before {
  content: "\e6fb";
}

.icon-link:before {
  content: "\e8fb";
}

.icon-alert:before {
  content: "\e61c";
}

.icon-alert1:before {
  content: "\e68e";
}

.icon-gitee:before {
  content: "\e608";
}

.icon-github:before {
  content: "\e607";
}

.icondialog:before {
  content: "\e82a";
}

.icon_map:before {
  content: "\e68a";
}

.iconbianjiqi:before {
  content: "\e7bd";
}

.icondivider:before {
  content: "\e6b3";
}

.iconanniu:before {
  content: "\e642";
}

.icon-button:before {
  content: "\e648";
}

.icon-button1:before {
  content: "\e705";
}

.iconadd:before {
  content: "\e602";
}

.icon-password:before {
  content: "\e600";
}

.icon-lock:before {
  content: "\e822";
}

.icon-html:before {
  content: "\e622";
}

.icon-time:before {
  content: "\e61a";
}

.icon-chart1:before {
  content: "\e60f";
}

.icon-timerange:before {
  content: "\e601";
}

.icon-checkbox:before {
  content: "\e629";
}

.icon-date:before {
  content: "\e7b2";
}

.icon-setting:before {
  content: "\e7fe";
}

.icon-slider:before {
  content: "\e620";
}

.icon-table:before {
  content: "\e802";
}

.icon-list:before {
  content: "\e7dc";
}

.icon-input:before {
  content: "\e619";
}

.icon-switch:before {
  content: "\e62f";
}

.icon-rate:before {
  content: "\e829";
}

.icon-att:before {
  content: "\e624";
}

.icon-doc:before {
  content: "\e621";
}

.icon-editor:before {
  content: "\e614";
}

.icon-textarea:before {
  content: "\e603";
}

.icon-location:before {
  content: "\e604";
}

.icon-col:before {
  content: "\e69d";
}

.icon-divider:before {
  content: "\e73e";
}

.icon-daterange:before {
  content: "\e73b";
}

.icon-select:before {
  content: "\e63c";
}

.icon-radio:before {
  content: "\e605";
}

.icon-chart:before {
  content: "\e606";
}

.icon-inputNumber:before {
  content: "\e6a7";
}

.icon-tab:before {
  content: "\e6b4";
}

