<template>
  <a-card style="margin-top: 5px" :bordered="false">
    <!-- <div style="font-size: 16px; font-weight: 600">竣工验收</div> -->
    <div v-has="'dgdoc-archives-list'" class="table-page-search-wrapper" style="margin-top: 10px">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="5" :sm="8">
            <a-form-item label="项目资料名称">
              <a-input placeholder="请输入项目资料名称" v-model="queryParam.archivesName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="8">
            <a-form-item label="上传状态">
              <a-select
                placeholder="请选择上传状态"
                v-model="queryParam.uploadState"
                allowClear
                :options="[
                  { label: '未上传', value: '0' },
                  { label: '部分上传', value: '1' },
                  { label: '已上传', value: '2' },
                ]"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="8">
            <a-form-item label="审核状态">
              <a-select
                placeholder="请选择审核状态"
                v-model="queryParam.auditState"
                allowClear
                :options="[
                  { label: '未审核', value: '0' },
                  { label: '部分审核', value: '1' },
                  { label: '已审核', value: '2' },
                  { label: '已退回', value: '3' },
                ]"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="loadData" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <div>
      <a-table
        :columns="columns"
        :scroll="{ x: 1500, y: '60vh' }"
        size="middle"
        bordered
        rowKey="archivesInstanceId"
        :pagination="false"
        :dataSource="dataSource"
        :loading="loading"
        :expandedRowKeys="expandedRowKeys"
        @expandedRowsChange="handleExpandedRowsChange"
        :expandIconColumnIndex="0"
        :customRow="customRow"
      >
        <span slot="archivesName" slot-scope="text">
          <j-ellipsis :value="text" :length="15" />
        </span>
        <!-- 上传状态 -->
        <span slot="uploadState" slot-scope="value, row">
          <div v-if="value">
            <a-tag :color="upTagColor[value].color"> {{ upTagColor[value].text }}</a-tag>
            <!-- <a-icon type="picture" style="color: #cccccc; cursor: pointer" @click="pictureClick(row)" /> -->
          </div>
        </span>
        <!-- 审核状态 -->
        <span slot="auditState" slot-scope="value">
          <div v-if="value">
            <a-tag :color="exTagColor[value].color"> {{ exTagColor[value].text }}</a-tag>
            <a-tooltip>
              <template slot="title"> prompt text </template>
              <a-icon v-if="value == 3" type="exclamation-circle" style="color: #f5222d; cursor: pointer" />
            </a-tooltip>
          </div>
        </span>
        <!-- 字符串超长截取省略号显示-->
        <span slot="component" slot-scope="text">
          <j-ellipsis :value="text" />
        </span>
        <span slot="uploadDate" slot-scope="value">
          <span v-if="value.uploadDate">{{ value.uploadDate }}</span>
          <span v-else>-</span>
        </span>
        <span slot="filePage" slot-scope="value">
          <a v-if="value.filePage">{{ value.filePage }}</a>
          <span v-else>-</span>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->
  </a-card>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import { getAction } from '@/api/manage'

const upTagColor = {
  0: { text: '未上传', color: '#CCCCCC' },
  1: { text: '部分上传', color: '#13C2C2' },
  2: { text: '已上传', color: '#1890FF' },
}

const exTagColor = {
  0: { text: '未审核', color: '#CCCCCC' },
  1: { text: '部分审核', color: '#13C2C2' },
  2: { text: '已审核', color: '#1890FF' },
  3: { text: '已退回', color: '#F5222D' },
}

const columns = [
  // {
  //   title: '编号',
  //   dataIndex: 'archivesNo',
  //   align: 'center',
  // },
  {
    title: '项目资料',
    dataIndex: 'archivesName',
    width: 400,
    scopedSlots: { customRender: 'archivesName' },
  },
  {
    title: '上传日期',
    dataIndex: 'uploadDate',
    align: 'center',
    scopedSlots: { customRender: 'uploadDate' },
  },
  {
    title: '页数',
    width: 60,
    dataIndex: 'filePage',
    align: 'center',
    scopedSlots: { customRender: 'filePage' },
  },
  {
    title: '上传状态',
    dataIndex: 'uploadState',
    align: 'center',
    scopedSlots: { customRender: 'uploadState' },
  },
  {
    title: '审核状态',
    dataIndex: 'auditState',
    align: 'center',
    scopedSlots: { customRender: 'auditState' },
  },
]

// 优化后的数据处理函数 - 避免深拷贝，直接修改原对象
function processDataSource(items) {
  if (!Array.isArray(items)) return items;

  // 使用 for 循环替代 map，性能更好
  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    // 直接修改原对象，避免创建新对象
    if (item.uploadDate == null) {
      item.uploadDate = '—';
    }
    if (item.filePage == null) {
      item.filePage = '—';
    }

    // 递归处理 children（如果存在）
    if (item.children && Array.isArray(item.children)) {
      processDataSource(item.children);
    }
  }

  return items;
}

export default {
  name: 'FileUploadList',
  mixins: [JeecgListMixin],
  components: {
    JEllipsis,
  },
  data() {
    return {
      upTagColor: upTagColor,
      exTagColor: exTagColor,
      description: '这是菜单管理页面',
      // 表头
      columns: columns,
      loading: false,
      // 展开的行，受控属性
      expandedRowKeys: [],
      url: {
        list: '/archives/list',
      },
      projectMonomerId: null,
    }
  },
  methods: {
    loadData() {
      this.dataSource = []
      var params = this.getQueryParams() //查询条件
      params.archivesType = '0'
      params.status = '0'
      params.projectMonomerId = this.projectMonomerId
      if (!params.projectMonomerId) return
      this.loading = true
      getAction(this.url.list, params)
        .then((res) => {
          if (res.success) {
            this.dataSource = res.result.records || res.result
            this.dataSource = processDataSource(this.dataSource)
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleExpandedRowsChange(expandedRows) {
      this.expandedRowKeys = expandedRows
    },
    // 点击图片图标
    pictureClick(row) {
      console.log(row)
    },
    customRow(record) {
      let backgroundColor
      if (record.uploadState == '2') {
        backgroundColor = '#F6FFED'
      }
      if (record.status == '1') {
        backgroundColor = '#f9f9f9'
      }
      // 在这里根据数据中的某个字段的值来决定行的样式
      return {
        style: {
          'box-sizing': 'content-box',
          'background-color': backgroundColor, // 根据字段值设置背景颜色
          color: record.status == '1' ? '#00000066' : '',
        },
      }
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
:deep(.ant-card-body) {
  padding: 10px 24px 10px 24px;
}
:deep(.ant-form-inline .ant-form-item) {
  margin-bottom: 0px;
}
</style>
