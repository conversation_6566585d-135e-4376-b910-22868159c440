<template>
  <a-card style="margin-top: 5px" :bordered="false">
    <!-- <div style="font-size: 16px; font-weight: 600">分部验收</div> -->
    <div v-has="'dgdoc-monomer-list'" style="margin-top: 10px">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="5" :sm="8">
            <a-form-item label="单体工程">
              <a-select
                style="max-width: 12vw"
                placeholder="请选择单体工程"
                v-model="queryParam.monomerIds"
                :options="monomerOption"
                :filter-option="filterOption"
                showSearch
                allowClear
                @select="selectMonomer"
                mode="multiple"
                :maxTagTextLength="5"
                :maxTagCount="1"
                :dropdownMatchSelectWidth="false"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="8">
            <a-form-item label="分部分项">
              <a-input placeholder="请输入分部分项" v-model="queryParam.archivesName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="8">
            <a-form-item label="上传状态">
              <a-select
                placeholder="请选择上传状态"
                v-model="queryParam.uploadState"
                allowClear
                :options="[
                  { label: '未上传', value: '0' },
                  { label: '部分上传', value: '1' },
                  { label: '已上传', value: '2' },
                ]"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="8">
            <a-form-item label="审核状态">
              <a-select
                placeholder="请选择审核状态"
                v-model="queryParam.auditState"
                allowClear
                :options="[
                  { label: '未审核', value: '0' },
                  { label: '部分审核', value: '1' },
                  { label: '已审核', value: '2' },
                  { label: '已退回', value: '3' },
                ]"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="8">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        :columns="[
          {
            title: '单体工程',
            dataIndex: 'monomerName',
            scopedSlots: { customRender: 'monomerName' },
          },
        ]"
        :scroll="{ y: '60vh' }"
        size="middle"
        bordered
        rowKey="monomerId"
        :pagination="false"
        :dataSource="dataSource"
        :loading="loading"
      >
        <div slot="monomerName" slot-scope="value, row">
          <div style="display: flex">
            <span>{{ value }}</span>
            <span v-if="row.monomerReportStatus == '1'">
              <a-tooltip>
                <template slot="title">
                  <span v-if="row.monomerSupDealResult === '0'">存在检测不合格报告-未销案</span>
                  <span v-if="row.monomerSupDealResult === '1'">存在检测不合格报告-已销案</span>
                </template>
                <a-icon
                  style="cursor: pointer; font-size: 20px"
                  type="info-circle"
                  theme="twoTone"
                  :two-tone-color="row.monomerSupDealResult === '0' ? '#F5222DFF' : '#06DF6C'"
                  @click="handleNonconformityReport(row)"
                />
              </a-tooltip>
            </span>
          </div>
        </div>
        <a-table
          slot="expandedRowRender"
          slot-scope="record"
          :columns="columns"
          size="middle"
          bordered
          rowKey="archivesId"
          :pagination="false"
          :dataSource="record.archives"
          :loading="loading"
          :expandedRowKeys="expandedRowKeys"
          @expandedRowsChange="handleExpandedRowsChange"
          :expandIconColumnIndex="0"
        >
          <span slot="archivesName" slot-scope="text">
            <j-ellipsis :value="text" :length="15" />
          </span>
          <!-- 上传状态 -->
          <span slot="uploadState" slot-scope="value, row">
            <div v-if="value">
              <a-tag :color="upTagColor[value].color"> {{ upTagColor[value].text }}</a-tag>
            </div>
          </span>
          <!-- 参建方确认 -->
          <span slot="cjEntConfirmState" slot-scope="value, row">
            <div>
              <a-tag :color="row.cjEntConfirmState ? '#1890FF' : '#CCCCCC'">
                {{ row.cjEntConfirmState == '1' ? '已确认' : '未确认' }}
              </a-tag>
              <!-- <a-icon
                type="picture"
                v-if="row.cjEntConfirmState"
                style="color: #1890ffff; cursor: pointer"
                @click="pictureClick(row.cjEntConfirmState)"
              /> -->
            </div>
          </span>
          <!-- 审核状态 -->
          <span slot="auditState" slot-scope="value">
            <div v-if="value">
              <a-tag :color="exTagColor[value].color"> {{ exTagColor[value].text }}</a-tag>
              <a-tooltip>
                <template slot="title"> prompt text </template>
                <a-icon v-if="value == 3" type="exclamation-circle" style="color: #f5222d; cursor: pointer" />
              </a-tooltip>
            </div>
          </span>
          <!-- 附件列表 -->
          <span slot="fileList" slot-scope="value">
            <!-- <span v-has="'dgdoc-monomer-update'"> -->
            <a v-if="value.length > 0" href="javascript:;" @click="handleFileList(value)">查看</a>
            <span v-else>-</span>
            <!-- </span> -->
          </span>
          <!-- 字符串超长截取省略号显示-->
          <span slot="component" slot-scope="text">
            <j-ellipsis :value="text" />
          </span>
          <span slot="uploadDate" slot-scope="value">
            <span v-if="value.uploadDate">{{ value.uploadDate }}</span>
            <span v-else>-</span>
          </span>
          <span slot="filePage" slot-scope="value">
            <a v-if="value.filePage">{{ value.filePage }}</a>
            <span v-else>-</span>
          </span>
        </a-table>
      </a-table>
    </div>
    <!-- table区域-end -->
    <PreviewModal ref="previewModal"></PreviewModal>
    <FileTableModal ref="fileTableModal"></FileTableModal>
    <NonconformityReportModal ref="nonconformityReportModal"></NonconformityReportModal>
  </a-card>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import PreviewModal from '../../../components/PreviewModal'
import FileTableModal from './FileTableModal'
import NonconformityReportModal from './NonconformityReportModal'
import { getAction, deleteAction, postAction } from '@/api/manage'
// 导入防抖函数
import { debounce } from 'lodash'

const upTagColor = {
  0: { text: '未上传', color: '#CCCCCC' },
  1: { text: '部分上传', color: '#13C2C2' },
  2: { text: '已上传', color: '#1890FF' },
}

const exTagColor = {
  0: { text: '未审核', color: '#CCCCCC' },
  1: { text: '部分审核', color: '#13C2C2' },
  2: { text: '已审核', color: '#1890FF' },
  3: { text: '已退回', color: '#F5222D' },
}

const columns = [
  // {
  //   title: '编号',
  //   dataIndex: 'archivesNo',
  //   align: 'center',
  // },
  {
    title: '分部分项',
    dataIndex: 'archivesName',
    width: 400,
    scopedSlots: { customRender: 'archivesName' },
  },
  {
    title: '上传日期',
    dataIndex: 'uploadDate',
    align: 'center',
    scopedSlots: { customRender: 'uploadDate' },
  },
  {
    title: '页数',
    width: 60,
    dataIndex: 'filePage',
    align: 'center',
    scopedSlots: { customRender: 'filePage' },
  },
  {
    title: '上传状态',
    dataIndex: 'uploadState',
    align: 'center',
    scopedSlots: { customRender: 'uploadState' },
  },
  {
    title: '参建方确认',
    dataIndex: 'cjEntConfirmState',
    align: 'center',
    scopedSlots: { customRender: 'cjEntConfirmState' },
  },
  {
    title: '审核状态',
    dataIndex: 'auditState',
    align: 'center',
    scopedSlots: { customRender: 'auditState' },
  },
  {
    title: '附件',
    dataIndex: 'fileList',
    align: 'center',
    scopedSlots: { customRender: 'fileList' },
  },
]

// 优化后的数据处理函数 - 避免深拷贝，直接修改原对象
function processDataSource(items) {
  if (!Array.isArray(items)) return items;

  // 使用 for 循环替代 map，性能更好
  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    // 直接修改原对象，避免创建新对象
    if (item.uploadDate == null) {
      item.uploadDate = '—';
    }
    if (item.filePage == null) {
      item.filePage = '—';
    }

    // 递归处理 children（如果存在）
    if (item.children && Array.isArray(item.children)) {
      processDataSource(item.children);
    }
  }

  return items;
}

export default {
  name: 'FileUploadList',
  mixins: [JeecgListMixin],
  components: {
    JEllipsis,
    PreviewModal,
    FileTableModal,
    NonconformityReportModal,
  },
  data() {
    return {
      upTagColor: upTagColor,
      exTagColor: exTagColor,
      description: '项目验收分布验收',
      // 表头
      columns: columns,
      loading: false,
      // 展开的行，受控属性
      expandedRowKeys: [],
      // 记录上次展开行数量，用于优化
      previousExpandedCount: 0,
      url: {
        list: '/archives/processList',
      },
      monomerListUrl: '',
      monomerOption: [],
      projectId: '',
    }
  },
  methods: {
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    getMonomerOption() {
      const that = this
      getAction(this.monomerListUrl).then((res) => {
        console.log(res)
        if (res.success) {
          this.$nextTick(() => {
            that.monomerOption = res.result.map((item) => {
              return {
                ...item,
                label: item.monomerName,
                value: item.monomerId,
              }
            })
          })
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    selectMonomer(value) {
      // this.loadData()
    },
    searchQuery() {
      this.loadData()
    },
    loadData() {
      this.dataSource = []
      var params = this.getQueryParams() //查询条件
      params.archivesType = '1'
      params.projectId = this.projectId
      // params.status = '0'
      if (!params.projectId) return
      this.loading = true
      getAction(this.url.list, params)
        .then((res) => {
          if (res.success) {
            this.dataSource = res.result.records || res.result

            // 优化：使用 requestAnimationFrame 分批处理数据，避免阻塞UI
            this.processDataInBatches(this.dataSource)
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 分批处理数据，避免长时间阻塞主线程
    processDataInBatches(dataSource, batchSize = 10) {
      let index = 0;
      const processBatch = () => {
        const endIndex = Math.min(index + batchSize, dataSource.length);

        for (let i = index; i < endIndex; i++) {
          const item = dataSource[i];
          if (item.archives) {
            processDataSource(item.archives);
          }
        }

        index = endIndex;

        if (index < dataSource.length) {
          // 使用 requestAnimationFrame 确保不阻塞UI渲染
          requestAnimationFrame(processBatch);
        }
      };

      processBatch();
    },
    // 添加防抖处理，避免频繁展开/收起操作
    handleExpandedRowsChange: debounce(function(expandedRows) {
      this.expandedRowKeys = expandedRows

      // 可选：预加载即将展开的行数据
      if (expandedRows.length > this.previousExpandedCount) {
        this.preloadExpandedData(expandedRows)
      }

      this.previousExpandedCount = expandedRows.length
    }, 100),

    // 预加载展开行数据（可选优化）
    preloadExpandedData(expandedRows) {
      // 这里可以添加预加载逻辑，比如预处理即将显示的数据
      console.log('预加载展开行数据:', expandedRows)
    },
    // 点击图片图标
    pictureClick(url) {
      this.$refs.previewModal.visible = true
      this.$refs.previewModal.url = url
      this.$refs.previewModal.fileType = url.substring(url.lastIndexOf('.') + 1)
    },
    // 附件列表
    handleFileList(value) {
      this.$refs.fileTableModal.visible = true
      this.$refs.fileTableModal.fileList = value
    },
    // 不合格报告记录
    handleNonconformityReport(row) {
      this.$refs.nonconformityReportModal.visible = true
      this.$refs.nonconformityReportModal.monomerId = row.monomerId
      this.$refs.nonconformityReportModal.loadData()
    },
  },
  watch: {
    monomerListUrl(value) {
      console.log(value)
      this.getMonomerOption()
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
:deep(.ant-card-body) {
  padding: 10px 24px 10px 24px;
}
:deep(.ant-form-inline .ant-form-item) {
  margin-bottom: 0px;
}
/* :deep(.ant-select ul) {
  display: flex;
  height: 32px;
} */
</style>
