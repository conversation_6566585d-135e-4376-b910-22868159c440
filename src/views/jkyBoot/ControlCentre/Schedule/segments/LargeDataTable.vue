<template>
  <div class="large-data-table">
    <!-- 数据量提示 -->
    <div class="data-info" v-if="showDataInfo">
      <a-alert
        :message="`当前数据量: ${dataSource.length} 条，已启用大数据优化模式`"
        type="info"
        show-icon
        closable
        style="margin-bottom: 16px"
      />
    </div>

    <!-- 虚拟滚动表格 -->
    <vxe-table
      ref="virtualTable"
      :data="dataSource"
      :height="tableHeight"
      :scroll-y="{ enabled: true, gt: 50 }"
      :scroll-x="{ enabled: true }"
      :expand-config="expandConfig"
      @toggle-tree-expand="handleVirtualExpand"
      :loading="loading"
      border
      stripe
      size="medium"
    >
      <!-- 展开列 -->
      <vxe-column type="expand" width="50" fixed="left">
        <template #content="{ row }">
          <div class="expand-content">
            <!-- 已加载的展开数据 -->
            <a-table
              v-if="expandedData[row.monomerId]"
              :columns="expandColumns"
              :dataSource="expandedData[row.monomerId]"
              :pagination="false"
              size="small"
              :scroll="{ x: 1200 }"
              bordered
            >
              <!-- 档案名称 -->
              <span slot="archivesName" slot-scope="text">
                <j-ellipsis :value="text" :length="15" />
              </span>
              
              <!-- 上传状态 -->
              <span slot="uploadState" slot-scope="value, row">
                <div v-if="value">
                  <a-tag :color="upTagColor[value].color">{{ upTagColor[value].text }}</a-tag>
                </div>
              </span>
              
              <!-- 参建方确认 -->
              <span slot="cjEntConfirmState" slot-scope="value, row">
                <div>
                  <a-tag :color="row.cjEntConfirmState ? '#1890FF' : '#CCCCCC'">
                    {{ row.cjEntConfirmState == '1' ? '已确认' : '未确认' }}
                  </a-tag>
                </div>
              </span>
              
              <!-- 附件列表 -->
              <span slot="fileList" slot-scope="value">
                <a-button
                  v-if="value && value.length > 0"
                  type="link"
                  size="small"
                  @click="handleFileList(value)"
                >
                  查看附件({{ value.length }})
                </a-button>
                <span v-else>-</span>
              </span>
            </a-table>
            
            <!-- 加载中状态 -->
            <div v-else-if="loadingExpanded[row.monomerId]" class="loading-container">
              <a-spin size="small" />
              <span style="margin-left: 8px;">正在加载详细数据...</span>
            </div>
            
            <!-- 点击加载 -->
            <div v-else class="click-to-load">
              <a-button 
                type="dashed" 
                size="small" 
                @click="loadExpandData(row)"
                :loading="loadingExpanded[row.monomerId]"
              >
                <a-icon type="down" />
                点击加载详细数据 ({{ row.archives ? row.archives.length : 0 }} 条)
              </a-button>
            </div>
          </div>
        </template>
      </vxe-column>
      
      <!-- 单体工程列 -->
      <vxe-column field="monomerName" title="单体工程" width="200" fixed="left">
        <template #default="{ row }">
          <div style="display: flex; align-items: center;">
            <span>{{ row.monomerName }}</span>
            <span v-if="row.monomerReportStatus == '1'" style="margin-left: 8px;">
              <a-tooltip>
                <template slot="title">
                  <span v-if="row.monomerSupDealResult === '0'">存在检测不合格报告-未销案</span>
                  <span v-if="row.monomerSupDealResult === '1'">存在检测不合格报告-已销案</span>
                </template>
                <a-icon
                  style="cursor: pointer; font-size: 18px;"
                  type="info-circle"
                  theme="twoTone"
                  :two-tone-color="row.monomerSupDealResult === '0' ? '#F5222DFF' : '#06DF6C'"
                  @click="$emit('nonconformity-report', row)"
                />
              </a-tooltip>
            </span>
          </div>
        </template>
      </vxe-column>
      
      <!-- 其他列可以根据需要添加 -->
      <vxe-column field="archivesCount" title="档案数量" width="100" align="center">
        <template #default="{ row }">
          <a-tag color="blue">{{ row.archives ? row.archives.length : 0 }}</a-tag>
        </template>
      </vxe-column>
      
      <vxe-column field="uploadProgress" title="上传进度" width="120" align="center">
        <template #default="{ row }">
          <a-progress 
            :percent="calculateUploadProgress(row)" 
            size="small" 
            :status="getProgressStatus(row)"
          />
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script>
import JEllipsis from '@/components/jeecg/JEllipsis'
import { tableOptimizer } from '@/utils/tableOptimizer'
import { debounce } from 'lodash'

const upTagColor = {
  0: { text: '未上传', color: '#CCCCCC' },
  1: { text: '部分上传', color: '#13C2C2' },
  2: { text: '已上传', color: '#1890FF' },
}

export default {
  name: 'LargeDataTable',
  components: {
    JEllipsis
  },
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    tableHeight: {
      type: [String, Number],
      default: '60vh'
    },
    showDataInfo: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      upTagColor,
      expandedData: {}, // 存储展开行的数据
      loadingExpanded: {}, // 存储展开行的加载状态
      expandConfig: {
        lazy: true,
        iconOpen: 'ant-table-row-expand-icon ant-table-row-expanded',
        iconClose: 'ant-table-row-expand-icon ant-table-row-collapsed'
      },
      // 展开表格的列配置
      expandColumns: [
        {
          title: '档案名称',
          dataIndex: 'archivesName',
          width: 300,
          scopedSlots: { customRender: 'archivesName' },
        },
        {
          title: '上传状态',
          dataIndex: 'uploadState',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'uploadState' },
        },
        {
          title: '参建方确认',
          dataIndex: 'cjEntConfirmState',
          align: 'center',
          width: 120,
          scopedSlots: { customRender: 'cjEntConfirmState' },
        },
        {
          title: '附件',
          dataIndex: 'fileList',
          align: 'center',
          width: 120,
          scopedSlots: { customRender: 'fileList' },
        }
      ]
    }
  },
  methods: {
    // 懒加载展开数据
    loadExpandData: debounce(async function(row) {
      if (this.expandedData[row.monomerId] || this.loadingExpanded[row.monomerId]) {
        return;
      }
      
      this.$set(this.loadingExpanded, row.monomerId, true);
      
      try {
        // 使用优化的数据处理
        let processedData = row.archives || [];
        
        if (processedData.length > 100) {
          // 大数据量使用 Web Worker 处理
          processedData = await tableOptimizer.processDataWithWorker(processedData, {
            onProgress: (progress) => {
              console.log(`处理进度: ${progress}%`);
            }
          });
        } else {
          // 小数据量直接处理
          processedData = tableOptimizer.processDataSource(processedData);
        }
        
        this.$set(this.expandedData, row.monomerId, processedData);
        
        // 触发展开
        this.$refs.virtualTable.setTreeExpand(row, true);
        
      } catch (error) {
        console.error('加载展开数据失败:', error);
        this.$message.error('加载数据失败，请重试');
      } finally {
        this.$set(this.loadingExpanded, row.monomerId, false);
      }
    }, 200),
    
    // 虚拟表格展开处理
    handleVirtualExpand({ row, expanded }) {
      if (expanded && !this.expandedData[row.monomerId]) {
        this.loadExpandData(row);
      }
    },
    
    // 计算上传进度
    calculateUploadProgress(row) {
      if (!row.archives || row.archives.length === 0) return 0;
      
      const uploadedCount = row.archives.filter(item => item.uploadState === '2').length;
      return Math.round((uploadedCount / row.archives.length) * 100);
    },
    
    // 获取进度状态
    getProgressStatus(row) {
      const progress = this.calculateUploadProgress(row);
      if (progress === 100) return 'success';
      if (progress > 0) return 'active';
      return 'normal';
    },
    
    // 处理附件列表
    handleFileList(fileList) {
      this.$emit('file-list', fileList);
    }
  },
  
  beforeDestroy() {
    // 清理资源
    tableOptimizer.destroy();
  }
}
</script>

<style scoped>
.large-data-table {
  width: 100%;
}

.expand-content {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
  margin: 8px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
}

.click-to-load {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

:deep(.vxe-table--border-line) {
  border-color: #e8e8e8;
}

:deep(.vxe-table .vxe-body--row.row--stripe) {
  background-color: #fafafa;
}
</style>
