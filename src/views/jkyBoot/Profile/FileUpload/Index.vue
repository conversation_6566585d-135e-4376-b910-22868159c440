<template>
  <div class="table-page-search-wrapper">
    <a-card v-has="'dgdoc-archives-list'" :bordered="false">
      <a-form layout="inline">
        <!-- @keyup.enter.native="searchQuery" -->
        <a-row :gutter="24">
          <a-col :md="8" :sm="8">
            <a-form-item label="选择项目">
              <a-select
                style="width: 250px"
                placeholder="请选择项目"
                v-model="paramData.projectId"
                :options="projectOption"
                @search="projectIdsearch"
                :filter-option="filterOption"
                showSearch
                @select="selectProject"
                :dropdownMatchSelectWidth="false"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="8">
            <a-form-item label="选择单体工程">
              <a-select
                style="width: 250px"
                placeholder="请选择单体工程"
                v-model="paramData.monomerId"
                :options="monomerOption"
                :filter-option="filterOption"
                showSearch
                @select="selectMonomer"
                :dropdownMatchSelectWidth="false"
              ></a-select>
              <!-- @search="monomerSearch" -->
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <a-card style="margin-top: 5px" :bordered="false">
      <div v-has="'dgdoc-archives-list'" class="table-page-search-wrapper" style="margin-top: 10px">
        <a-form layout="inline" @keyup.enter.native="searchQuery">
          <a-row :gutter="24">
            <a-col :md="6" :sm="8">
              <a-form-item label="分部分项">
                <a-input placeholder="请输入分部分项" v-model="queryParam.archivesName"></a-input>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="8">
              <a-form-item label="上传状态">
                <a-select
                  placeholder="请选择上传状态"
                  v-model="queryParam.uploadState"
                  allowClear
                  :options="[
                    { label: '未上传', value: '0' },
                    { label: '部分上传', value: '1' },
                    { label: '已上传', value: '2' },
                  ]"
                ></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="8">
              <a-form-item label="审核状态">
                <a-select
                  placeholder="请选择审核状态"
                  v-model="queryParam.auditState"
                  allowClear
                  :options="[
                    { label: '未审核', value: '0' },
                    { label: '部分审核', value: '1' },
                    { label: '已审核', value: '2' },
                    { label: '已退回', value: '3' },
                  ]"
                ></a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="8">
              <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                <a-button type="primary" @click="loadData(true)" icon="search">查询</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <!-- 操作按钮区域 -->
      <div class="table-operator">
        <a-button
          v-has="'dgdoc-archives-audit'"
          type="primary"
          :disabled="!(selectedRowKeys.length > 0)"
          @click="handleVerify(selectedRowKeys)"
          >批量确认</a-button
        >
      </div>

      <!-- 数据量切换提示 -->
      <div v-if="showModeSwitch" class="mode-switch-alert">
        <a-alert
          :message="`检测到大数据量 (${totalDataCount} 条记录)，建议切换到优化模式以提升性能`"
          type="warning"
          show-icon
          action
          style="margin-bottom: 16px"
        >
          <template #action>
            <a-button size="small" type="primary" @click="switchToLargeDataMode">
              切换到优化模式
            </a-button>
            <a-button size="small" @click="dismissModeSwitch" style="margin-left: 8px;">
              继续使用当前模式
            </a-button>
          </template>
        </a-alert>
      </div>

      <!-- table区域-begin -->
      <div>
        <!-- 大数据量优化表格 -->
        <LargeDataFileTable
          v-if="useLargeDataMode"
          :dataSource="dataSource"
          :loading="loading"
          :selectedRowKeys="selectedRowKeys"
          @selection-change="handleSelectionChange"
          @upload="handleUpload"
          @preview="handlPreview"
          @delete="handlDelete"
          @verify="handleVerify"
          @back="handleBack"
        />

        <!-- 原始表格 -->
        <div v-else>
          <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
            <i class="anticon anticon-info-circle ant-alert-icon"></i>已选择&nbsp;<a style="font-weight: 600">{{
              selectedRowKeys.length
            }}</a
            >项&nbsp;&nbsp;
            <a-popconfirm title="确定清空选项吗?" @confirm="() => onClearSelected()">
              <a style="margin-left: 24px">清空选项</a>
            </a-popconfirm>
          </div>

          <a-table
          v-if="tableShow"
          :defaultExpandAllRows="defaultExpandAllRows"
          :columns="columns"
          :scroll="{ x: 1500, y: '60vh' }"
          size="middle"
          bordered
          rowKey="archivesInstanceId"
          :pagination="false"
          :dataSource="dataSource"
          :loading="loading"
          :expandedRowKeys="expandedRowKeys"
          :rowSelection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
            getCheckboxProps: (row) => ({
              props: {
                disabled: !(row.status == '0' && row.uploadState == '2' && row.auditState == '0' && !row.children),
              },
            }),
          }"
          @expandedRowsChange="handleExpandedRowsChange"
          :expandIconColumnIndex="1"
          :customRow="customRow"
        >
          <span slot="action" slot-scope="text, row">
            <span v-has="'dgdoc-archives-upload'" v-if="row.status == '0' && row.uploadState == '0' && !row.children">
              <a @click="handleUpload(row)">上传资料</a>
              <a-divider type="vertical" />
            </span>
            <span v-has="'dgdoc-archives-upload'" v-if="row.auditState == '3' && !row.children">
              <a @click="handleUpload(row)">重新上传</a>
              <a-divider type="vertical" />
            </span>
            <span v-has="'dgdoc-archives-invalid'" v-if="row.status == '0' && row.uploadState == '0'">
              <a @click="handlEuninvolved(row, 0)">无此项</a>
              <a-divider v-if="row.status == '0' && row.uploadState == '2'" type="vertical" />
            </span>
            <span v-has="'dgdoc-archives-invalid'" v-if="row.status == '1' && !row.children">
              <a @click="handlEuninvolved(row, 1)">恢复</a>
            </span>

            <span v-has="'dgdoc-archives-preview'" v-if="row.status == '0' && row.uploadState == '2' && !row.children">
              <a @click="handlPreview(row)">预览</a>
              <a-divider
                v-if="row.status == '0' && row.uploadState == '2' && row.auditState == '0' && !row.children"
                type="vertical"
              />
            </span>

            <span
              v-has="'dgdoc-archives-file-remove'"
              v-if="row.status == '0' && row.uploadState == '2' && row.auditState == '0' && !row.children"
            >
              <a-popconfirm title="确定删除已上传文件吗?" @confirm="() => handlDelete(row)">
                <a>删除</a>
              </a-popconfirm>
              <a-divider type="vertical" />
            </span>
            <span
              v-has="'dgdoc-archives-upload'"
              v-if="row.status == '0' && row.uploadState == '2' && row.auditState == '0' && !row.children"
            >
              <a @click="handleUpload(row)">替换</a>
              <a-divider type="vertical" />
            </span>

            <span
              v-has="'dgdoc-archives-audit'"
              v-if="row.status == '0' && row.uploadState == '2' && row.auditState == '0' && !row.children"
            >
              <a-popconfirm title="确定通过吗?" @confirm="() => handleVerify([row.archivesInstanceId])">
                <a>确认</a>
              </a-popconfirm>
              <a-divider type="vertical" />
            </span>
            <span
              v-has="'dgdoc-archives-return'"
              v-if="row.status == '0' && row.uploadState == '2' && row.auditState == '0' && !row.children"
            >
              <a @click="handleBack(row)">退回</a>
            </span>
          </span>
          <span slot="archivesName" slot-scope="text">
            <j-ellipsis :value="text" :length="15" />
          </span>
          <!-- 上传状态 -->
          <span slot="uploadState" slot-scope="value, row">
            <div v-if="value">
              <a-tag :color="upTagColor[value].color"> {{ upTagColor[value].text }}</a-tag>
              <!-- <a-icon type="picture" style="color: #cccccc; cursor: pointer" @click="pictureClick(row)" /> -->
            </div>
          </span>
          <!-- 审核状态 -->
          <span slot="auditState" slot-scope="value, row">
            <div v-if="value">
              <a-tag :color="exTagColor[value].color"> {{ exTagColor[value].text }}</a-tag>
              <a-tooltip>
                <template slot="title"> prompt text </template>
                <a-icon v-if="value == 3" type="exclamation-circle" style="color: #f5222d; cursor: pointer" />
              </a-tooltip>
            </div>
          </span>
          <!-- 字符串超长截取省略号显示-->
          <span slot="component" slot-scope="text">
            <j-ellipsis :value="text" />
          </span>
          </a-table>
        </div>
      </div>
      <!-- table区域-end -->
      <form id="nodeDataFileUploadForm" style="display: none">
        <input type="file" accept=" .pdf, .png" id="nodeDataFileUpload" @change="uploadNodeData" />
      </form>
      <BackModal ref="backmodalForm" @ok="backModalOK"></BackModal>
      <PreviewModal ref="previewModal"></PreviewModal>
    </a-card>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import BackModal from './segments/backModal'
import PreviewModal from '../../components/PreviewModal'
import LargeDataFileTable from './LargeDataFileTable'
import { getAction, deleteAction, postAction } from '@/api/manage'
import { tableOptimizer } from '@/utils/tableOptimizer'

const upTagColor = {
  0: { text: '未上传', color: '#CCCCCC' },
  1: { text: '部分上传', color: '#13C2C2' },
  2: { text: '已上传', color: '#1890FF' },
}

const exTagColor = {
  0: { text: '未审核', color: '#CCCCCC' },
  1: { text: '部分审核', color: '#13C2C2' },
  2: { text: '已审核', color: '#1890FF' },
  3: { text: '已退回', color: '#F5222D' },
}

const columns = [
  // {
  //   title: '编号',
  //   dataIndex: 'archivesNo',
  //   align: 'center',
  // },
  {
    title: '分部分项',
    dataIndex: 'archivesName',
    width: 400,
    scopedSlots: { customRender: 'archivesName' },
  },
  {
    title: '上传日期',
    dataIndex: 'uploadDate',
    align: 'center',
  },
  {
    title: '页数',
    width: 60,
    dataIndex: 'filePage',
    align: 'center',
  },
  {
    title: '上传状态',
    dataIndex: 'uploadState',
    align: 'center',
    scopedSlots: { customRender: 'uploadState' },
  },
  {
    title: '审核状态',
    dataIndex: 'auditState',
    align: 'center',
    scopedSlots: { customRender: 'auditState' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    scopedSlots: { customRender: 'action' },
    align: 'center',
    width: 250,
  },
]

// 优化后的数据处理函数 - 避免深拷贝，直接修改原对象
function processDataSource(items) {
  if (!Array.isArray(items)) return items;

  // 使用 for 循环替代 map，性能更好
  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    // 直接修改原对象，避免创建新对象
    if (item.uploadDate == null) {
      item.uploadDate = '—';
    }
    if (item.filePage == null) {
      item.filePage = '—';
    }

    // 递归处理 children（如果存在）
    if (item.children && Array.isArray(item.children)) {
      processDataSource(item.children);
    }
  }

  return items;
}

export default {
  name: 'FileUploadList',
  mixins: [JeecgListMixin],
  components: {
    JEllipsis,
    BackModal,
    PreviewModal,
    LargeDataFileTable,
  },
  data() {
    return {
      upTagColor: upTagColor,
      exTagColor: exTagColor,
      description: '档案上传页面',
      paramData: {
        // projectId: null,
        // monomerId: null,
      },
      // 表头
      columns: columns,
      loading: false,
      // 展开的行，受控属性
      expandedRowKeys: [],
      // 大数据量模式相关
      useLargeDataMode: false,
      showModeSwitch: false,
      largeDataThreshold: 300, // 大数据量阈值
      url: {
        list: '/archives/list',
        monomerList: '',
        upload: '',
      },
      selectedRowKeys: [],
      projectOption: [],
      monomerOption: [],
      queryParam: {},
      defaultExpandAllRows: false,
      tableShow: true,
    }
  },
  computed: {
    // 计算总数据量
    totalDataCount() {
      let total = this.dataSource.length
      this.dataSource.forEach(item => {
        if (item.children && Array.isArray(item.children)) {
          total += item.children.length
        }
      })
      return total
    }
  },
  methods: {
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    getProjectOption(projectName, ifFirst = false) {
      const that = this
      // if (!projectName && projectName === '') return
      getAction('/project/list', { projectName }).then((res) => {
        if (res.success) {
          this.$nextTick(() => {
            that.projectOption = res.result.records.map((item) => {
              return {
                ...item,
                label: item.projectName,
                value: item.projectId,
              }
            })
            if (ifFirst && that.projectOption[0]) {
              that.paramData.projectId = that.projectOption[0].value
              that.selectProject(that.projectOption[0].value)
            }
          })
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    projectIdsearch(value) {
      this.getProjectOption(value)
    },
    selectProject(value) {
      this.url.monomerList = `/monomer/list/${value}`
      this.getMonomerOption()
      this.queryParam = {
        ...this.queryParam,
        archivesName: '',
        uploadState: '',
        auditState: '',
      }
      // this.monomerOption = []
    },
    getMonomerOption() {
      const that = this
      getAction(this.url.monomerList).then((res) => {
        if (res.success) {
          this.$nextTick(() => {
            that.monomerOption = res.result.map((item) => {
              return {
                ...item,
                label: item.monomerName,
                value: item.monomerId,
              }
            })
            if (that.monomerOption[0]) {
              that.paramData.monomerId = that.monomerOption[0].value
              that.selectMonomer(that.monomerOption[0].value)
            } else {
              that.paramData.monomerId = ''
              that.monomerOption = []
              this.dataSource = []
            }
          })
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    selectMonomer(value) {
      this.queryParam.projectMonomerId = value
      this.queryParam = {
        ...this.queryParam,
        archivesName: '',
        uploadState: '',
        auditState: '',
      }
      this.loadData()
    },
    loadData(ifSearch = false) {
      const params = this.getQueryParams() //查询条件
      this.dataSource = []
      this.tableShow = false
      const { archivesName, uploadState, auditState } = params
      if (!ifSearch) {
        this.$nextTick(() => {
          this.expandedRowKeys = []
        })
      }
      if (ifSearch && (!!archivesName || !!uploadState || !!auditState)) {
        this.defaultExpandAllRows = true
      } else {
        this.defaultExpandAllRows = false
      }
      params.archivesType = '1'
      if (!params.projectMonomerId) {
        this.tableShow = true
        return
      }
      this.loading = true
      getAction(this.url.list, params)
        .then((res) => {
          if (res.success) {
            this.dataSource = res.result.records || res.result

            // 检测数据量并决定是否显示模式切换提示
            this.checkDataVolumeAndOptimize()

            // 优化数据处理
            this.processDataOptimized()
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.tableShow = true
          this.loading = false
        })
    },

    // 检测数据量并优化
    checkDataVolumeAndOptimize() {
      const totalRecords = this.totalDataCount

      if (totalRecords > this.largeDataThreshold) {
        // 自动切换到大数据模式或显示切换提示
        if (totalRecords > this.largeDataThreshold * 2) {
          // 数据量很大，自动切换
          this.useLargeDataMode = true
          this.$message.info(`检测到大数据量 (${totalRecords} 条)，已自动启用优化模式`)
        } else {
          // 数据量较大，显示切换提示
          this.showModeSwitch = true
        }
      }
    },

    // 优化的数据处理
    async processDataOptimized() {
      if (this.dataSource.length > 100) {
        // 大数据量使用分批处理
        await tableOptimizer.processDataInBatches(this.dataSource, (progress) => {
          console.log(`数据处理进度: ${progress.percentage}%`)
        })
      } else {
        // 小数据量直接处理
        this.dataSource = processDataSource(this.dataSource)
      }
    },

    // 切换到大数据模式
    switchToLargeDataMode() {
      this.useLargeDataMode = true
      this.showModeSwitch = false
      this.$message.success('已切换到大数据优化模式，表格性能将显著提升')
    },

    // 忽略模式切换提示
    dismissModeSwitch() {
      this.showModeSwitch = false
    },

    // 处理选择变化（大数据模式）
    handleSelectionChange(keys) {
      this.selectedRowKeys = keys
    },
    // 打开数据规则编辑
    handleDataRule(record) {
      this.$refs.PermissionDataRuleList.edit(record)
    },

    handleExpandedRowsChange(expandedRows) {
      this.expandedRowKeys = expandedRows
    },
    onSelectChange(expandedKeys, expandedRows) {
      this.selectedRowKeys = expandedKeys
      // this.selectionRows = expandedRows
    },
    // 点击图片图标
    pictureClick(row) {
      console.log(row)
    },
    //文件上传
    handleUpload({ archivesInstanceId }) {
      this.url.upload = `/archives/upload/${archivesInstanceId}`
      document.getElementById('nodeDataFileUpload')?.click()
    },
    uploadNodeData(e) {
      const { files } = e.target
      const fileData = new FormData()
      fileData.append('file', files[0])
      this.loading = true
      postAction(this.url.upload, fileData)
        .then((res) => {
          console.log(res.result)
          if (res.success) {
            this.loadData(true)
            this.$message.success(res.message)
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
      document.getElementById('nodeDataFileUploadForm')?.reset()
    },
    // 作废恢复
    handlEuninvolved({ archivesInstanceId }, type) {
      const url = `/archives/invalid/${archivesInstanceId}`
      const params = {
        status: type === 0 ? '1' : '0',
      }
      getAction(url, params).then((res) => {
        if (res.success) {
          this.loadData(true)
          this.$message.success(res.message)
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handlPreview(row) {
      this.$refs.previewModal.visible = true
      this.$refs.previewModal.url = row.fileUrl
      this.$refs.previewModal.fileType = row.fileType
    },
    handlDelete({ archivesInstanceId }) {
      const url = `/archives/file/${archivesInstanceId}`
      deleteAction(url).then((res) => {
        if (res.success) {
          this.loadData(true)
          this.$message.success(res.message)
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleVerify(archivesInstanceIds) {
      const url = `/archives/audit/${archivesInstanceIds}`
      getAction(url).then((res) => {
        if (res.success) {
          this.loadData(true)
          this.selectedRowKeys = []
          this.$message.success(res.message)
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleBack(row) {
      this.$refs.backmodalForm.edit({ returnBackReason: '1' })
      this.$refs.backmodalForm.title = '退回'
      this.$refs.backmodalForm.disableSubmit = false
      this.$refs.backmodalForm.archivesInstanceId = row.archivesInstanceId
    },
    backModalOK() {
      this.loadData(true)
    },
    customRow(record) {
      let backgroundColor
      if (record.uploadState == '2') {
        backgroundColor = '#F6FFED'
      }
      if (record.status == '1') {
        backgroundColor = '#f9f9f9'
      }
      // 在这里根据数据中的某个字段的值来决定行的样式
      return {
        style: {
          'box-sizing': 'content-box',
          'background-color': backgroundColor, // 根据字段值设置背景颜色
          color: record.status == '1' ? '#00000066' : '',
        },
      }
    },
    searchReset() {
      this.queryParam = {
        ...this.queryParam,
        archivesName: '',
        uploadState: '',
        auditState: '',
      }
      this.loadData()
    },
  },
  mounted() {
    this.getProjectOption('', true)
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';

:deep(.ant-card-body) {
  padding: 10px 24px 10px 24px;
}

:deep(.ant-form-inline .ant-form-item) {
  margin-bottom: 0px;
}

.mode-switch-alert {
  margin-bottom: 16px;
}

.mode-switch-alert :deep(.ant-alert-action) {
  margin-left: auto;
}

/* 大数据模式样式优化 */
:deep(.vxe-table) {
  font-size: 13px;
}

:deep(.vxe-table .vxe-header--column) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.vxe-table .vxe-body--row:hover) {
  background-color: #e6f7ff;
}

/* 性能提示样式 */
.performance-tip {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.performance-tip .anticon {
  margin-right: 8px;
  font-size: 16px;
}
</style>
