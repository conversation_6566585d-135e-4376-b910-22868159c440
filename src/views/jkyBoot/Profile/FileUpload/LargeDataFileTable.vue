<template>
  <div class="large-data-file-table">
    <!-- 数据量提示 -->
    <div class="data-info" v-if="showDataInfo">
      <a-alert
        :message="`当前数据量: ${totalRecords} 条记录，已启用大数据优化模式`"
        type="info"
        show-icon
        closable
        style="margin-bottom: 16px"
      />
    </div>

    <!-- 选择提示 -->
    <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
      <i class="anticon anticon-info-circle ant-alert-icon"></i>已选择&nbsp;<a style="font-weight: 600">{{
        selectedRowKeys.length
      }}</a>项&nbsp;&nbsp;
      <a-popconfirm title="确定清空选项吗?" @confirm="onClearSelected">
        <a style="margin-left: 24px">清空选项</a>
      </a-popconfirm>
    </div>

    <!-- 虚拟滚动表格 -->
    <vxe-table
      ref="virtualTable"
      :data="dataSource"
      :height="tableHeight"
      :scroll-y="{ enabled: true, gt: 50 }"
      :scroll-x="{ enabled: true }"
      :expand-config="expandConfig"
      :checkbox-config="checkboxConfig"
      @toggle-tree-expand="handleVirtualExpand"
      @checkbox-change="handleCheckboxChange"
      @checkbox-all="handleCheckboxAll"
      :loading="loading"
      border
      stripe
      size="medium"
      :row-class-name="getRowClassName"
    >
      <!-- 选择列 -->
      <vxe-column type="checkbox" width="50" fixed="left" />
      
      <!-- 展开列 -->
      <vxe-column type="expand" width="50" fixed="left">
        <template #content="{ row }">
          <div class="expand-content">
            <!-- 已加载的展开数据 -->
            <div v-if="expandedData[row.archivesInstanceId]" class="expanded-table">
              <a-table
                :columns="expandColumns"
                :dataSource="expandedData[row.archivesInstanceId]"
                :pagination="false"
                size="small"
                :scroll="{ x: 1200 }"
                bordered
                :rowSelection="{
                  selectedRowKeys: getChildSelectedKeys(row.archivesInstanceId),
                  onChange: (keys) => handleChildSelection(row.archivesInstanceId, keys),
                  getCheckboxProps: (childRow) => ({
                    props: {
                      disabled: !(childRow.status == '0' && childRow.uploadState == '2' && childRow.auditState == '0'),
                    },
                  }),
                }"
              >
                <!-- 分部分项 -->
                <span slot="archivesName" slot-scope="text">
                  <j-ellipsis :value="text" :length="15" />
                </span>
                
                <!-- 上传状态 -->
                <span slot="uploadState" slot-scope="value">
                  <div v-if="value">
                    <a-tag :color="upTagColor[value].color">{{ upTagColor[value].text }}</a-tag>
                  </div>
                </span>
                
                <!-- 审核状态 -->
                <span slot="auditState" slot-scope="value">
                  <div v-if="value">
                    <a-tag :color="exTagColor[value].color">{{ exTagColor[value].text }}</a-tag>
                    <a-tooltip v-if="value == 3">
                      <template slot="title">已退回</template>
                      <a-icon type="exclamation-circle" style="color: #f5222d; cursor: pointer" />
                    </a-tooltip>
                  </div>
                </span>
                
                <!-- 操作列 -->
                <span slot="action" slot-scope="text, childRow">
                  <div class="action-buttons">
                    <a-button
                      v-if="childRow.status == '0' && childRow.uploadState == '0'"
                      type="link"
                      size="small"
                      @click="$emit('upload', childRow)"
                    >
                      上传资料
                    </a-button>
                    
                    <a-button
                      v-if="childRow.auditState == '3'"
                      type="link"
                      size="small"
                      @click="$emit('upload', childRow)"
                    >
                      重新上传
                    </a-button>
                    
                    <a-button
                      v-if="childRow.status == '0' && childRow.uploadState == '2'"
                      type="link"
                      size="small"
                      @click="$emit('preview', childRow)"
                    >
                      预览
                    </a-button>
                    
                    <a-popconfirm
                      v-if="childRow.status == '0' && childRow.uploadState == '2' && childRow.auditState == '0'"
                      title="确定删除已上传文件吗?"
                      @confirm="$emit('delete', childRow)"
                    >
                      <a-button type="link" size="small" danger>删除</a-button>
                    </a-popconfirm>
                    
                    <a-popconfirm
                      v-if="childRow.status == '0' && childRow.uploadState == '2' && childRow.auditState == '0'"
                      title="确定通过吗?"
                      @confirm="$emit('verify', [childRow.archivesInstanceId])"
                    >
                      <a-button type="link" size="small">确认</a-button>
                    </a-popconfirm>
                    
                    <a-button
                      v-if="childRow.status == '0' && childRow.uploadState == '2' && childRow.auditState == '0'"
                      type="link"
                      size="small"
                      @click="$emit('back', childRow)"
                    >
                      退回
                    </a-button>
                  </div>
                </span>
              </a-table>
            </div>
            
            <!-- 加载中状态 -->
            <div v-else-if="loadingExpanded[row.archivesInstanceId]" class="loading-container">
              <a-spin size="small" />
              <span style="margin-left: 8px;">正在加载子项数据...</span>
            </div>
            
            <!-- 点击加载 -->
            <div v-else class="click-to-load">
              <a-button 
                type="dashed" 
                size="small" 
                @click="loadExpandData(row)"
                :loading="loadingExpanded[row.archivesInstanceId]"
              >
                <a-icon type="down" />
                点击加载子项 ({{ row.children ? row.children.length : 0 }} 条)
              </a-button>
            </div>
          </div>
        </template>
      </vxe-column>
      
      <!-- 分部分项列 -->
      <vxe-column field="archivesName" title="分部分项" width="400" fixed="left">
        <template #default="{ row }">
          <j-ellipsis :value="row.archivesName" :length="20" />
        </template>
      </vxe-column>
      
      <!-- 上传日期 -->
      <vxe-column field="uploadDate" title="上传日期" width="120" align="center">
        <template #default="{ row }">
          {{ row.uploadDate || '—' }}
        </template>
      </vxe-column>
      
      <!-- 页数 -->
      <vxe-column field="filePage" title="页数" width="80" align="center">
        <template #default="{ row }">
          {{ row.filePage || '—' }}
        </template>
      </vxe-column>
      
      <!-- 上传状态 -->
      <vxe-column field="uploadState" title="上传状态" width="100" align="center">
        <template #default="{ row }">
          <a-tag v-if="row.uploadState" :color="upTagColor[row.uploadState].color">
            {{ upTagColor[row.uploadState].text }}
          </a-tag>
        </template>
      </vxe-column>
      
      <!-- 审核状态 -->
      <vxe-column field="auditState" title="审核状态" width="100" align="center">
        <template #default="{ row }">
          <div v-if="row.auditState">
            <a-tag :color="exTagColor[row.auditState].color">
              {{ exTagColor[row.auditState].text }}
            </a-tag>
            <a-tooltip v-if="row.auditState == 3">
              <template slot="title">已退回</template>
              <a-icon type="exclamation-circle" style="color: #f5222d; cursor: pointer; margin-left: 4px;" />
            </a-tooltip>
          </div>
        </template>
      </vxe-column>
      
      <!-- 子项统计 -->
      <vxe-column field="childrenCount" title="子项数量" width="100" align="center">
        <template #default="{ row }">
          <a-tag color="blue">{{ row.children ? row.children.length : 0 }}</a-tag>
        </template>
      </vxe-column>
      
      <!-- 完成进度 -->
      <vxe-column field="progress" title="完成进度" width="120" align="center">
        <template #default="{ row }">
          <a-progress 
            :percent="calculateProgress(row)" 
            size="small" 
            :status="getProgressStatus(row)"
          />
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script>
import JEllipsis from '@/components/jeecg/JEllipsis'
import { tableOptimizer } from '@/utils/tableOptimizer'
import { debounce } from 'lodash'

const upTagColor = {
  0: { text: '未上传', color: '#CCCCCC' },
  1: { text: '部分上传', color: '#13C2C2' },
  2: { text: '已上传', color: '#1890FF' },
}

const exTagColor = {
  0: { text: '未审核', color: '#CCCCCC' },
  1: { text: '部分审核', color: '#13C2C2' },
  2: { text: '已审核', color: '#1890FF' },
  3: { text: '已退回', color: '#F5222D' },
}

export default {
  name: 'LargeDataFileTable',
  components: {
    JEllipsis
  },
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    tableHeight: {
      type: [String, Number],
      default: '60vh'
    },
    showDataInfo: {
      type: Boolean,
      default: true
    },
    selectedRowKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      upTagColor,
      exTagColor,
      expandedData: {}, // 存储展开行的数据
      loadingExpanded: {}, // 存储展开行的加载状态
      childSelectedKeys: {}, // 存储子项选中状态
      expandConfig: {
        lazy: true,
        iconOpen: 'ant-table-row-expand-icon ant-table-row-expanded',
        iconClose: 'ant-table-row-expand-icon ant-table-row-collapsed'
      },
      checkboxConfig: {
        checkField: 'checked',
        halfField: 'indeterminate'
      },
      // 展开表格的列配置
      expandColumns: [
        {
          title: '分部分项',
          dataIndex: 'archivesName',
          width: 300,
          scopedSlots: { customRender: 'archivesName' },
        },
        {
          title: '上传日期',
          dataIndex: 'uploadDate',
          align: 'center',
          width: 120,
        },
        {
          title: '页数',
          dataIndex: 'filePage',
          align: 'center',
          width: 80,
        },
        {
          title: '上传状态',
          dataIndex: 'uploadState',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'uploadState' },
        },
        {
          title: '审核状态',
          dataIndex: 'auditState',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'auditState' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 300,
          scopedSlots: { customRender: 'action' },
        }
      ]
    }
  },
  computed: {
    totalRecords() {
      let total = this.dataSource.length
      this.dataSource.forEach(item => {
        if (item.children && Array.isArray(item.children)) {
          total += item.children.length
        }
      })
      return total
    }
  },
  methods: {
    // 懒加载展开数据
    loadExpandData: debounce(async function(row) {
      if (this.expandedData[row.archivesInstanceId] || this.loadingExpanded[row.archivesInstanceId]) {
        return;
      }
      
      this.$set(this.loadingExpanded, row.archivesInstanceId, true);
      
      try {
        // 使用优化的数据处理
        let processedData = row.children || [];
        
        if (processedData.length > 100) {
          // 大数据量使用 Web Worker 处理
          processedData = await tableOptimizer.processDataWithWorker(processedData);
        } else {
          // 小数据量直接处理
          processedData = tableOptimizer.processDataSource(processedData);
        }
        
        this.$set(this.expandedData, row.archivesInstanceId, processedData);
        
        // 触发展开
        this.$refs.virtualTable.setTreeExpand(row, true);
        
      } catch (error) {
        console.error('加载展开数据失败:', error);
        this.$message.error('加载数据失败，请重试');
      } finally {
        this.$set(this.loadingExpanded, row.archivesInstanceId, false);
      }
    }, 200),
    
    // 虚拟表格展开处理
    handleVirtualExpand({ row, expanded }) {
      if (expanded && !this.expandedData[row.archivesInstanceId]) {
        this.loadExpandData(row);
      }
    },
    
    // 计算完成进度
    calculateProgress(row) {
      if (!row.children || row.children.length === 0) return 0;
      
      const completedCount = row.children.filter(item => item.auditState === '2').length;
      return Math.round((completedCount / row.children.length) * 100);
    },
    
    // 获取进度状态
    getProgressStatus(row) {
      const progress = this.calculateProgress(row);
      if (progress === 100) return 'success';
      if (progress > 0) return 'active';
      return 'normal';
    },
    
    // 行样式
    getRowClassName({ row }) {
      if (row.uploadState == '2') return 'uploaded-row';
      if (row.status == '1') return 'disabled-row';
      return '';
    },
    
    // 处理选择框变化
    handleCheckboxChange({ records }) {
      const keys = records.map(row => row.archivesInstanceId);
      this.$emit('selection-change', keys);
    },
    
    // 处理全选
    handleCheckboxAll({ records }) {
      const keys = records.map(row => row.archivesInstanceId);
      this.$emit('selection-change', keys);
    },
    
    // 获取子项选中的keys
    getChildSelectedKeys(parentId) {
      return this.childSelectedKeys[parentId] || [];
    },
    
    // 处理子项选择
    handleChildSelection(parentId, keys) {
      this.$set(this.childSelectedKeys, parentId, keys);
      
      // 合并所有选中的keys并触发事件
      const allSelectedKeys = [...this.selectedRowKeys];
      Object.values(this.childSelectedKeys).forEach(childKeys => {
        allSelectedKeys.push(...childKeys);
      });
      
      this.$emit('selection-change', [...new Set(allSelectedKeys)]);
    },
    
    // 清空选择
    onClearSelected() {
      this.childSelectedKeys = {};
      this.$refs.virtualTable.clearCheckboxRow();
      this.$emit('selection-change', []);
    }
  },
  
  beforeDestroy() {
    // 清理资源
    tableOptimizer.destroy();
  }
}
</script>

<style scoped>
.large-data-file-table {
  width: 100%;
}

.expand-content {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
  margin: 8px;
}

.expanded-table {
  background-color: white;
  border-radius: 4px;
  padding: 8px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
}

.click-to-load {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.action-buttons .ant-btn {
  padding: 0 4px;
  height: 24px;
  font-size: 12px;
}

:deep(.vxe-table--border-line) {
  border-color: #e8e8e8;
}

:deep(.vxe-table .vxe-body--row.row--stripe) {
  background-color: #fafafa;
}

:deep(.uploaded-row) {
  background-color: #f6ffed !important;
}

:deep(.disabled-row) {
  background-color: #f9f9f9 !important;
  color: #00000066 !important;
}

:deep(.vxe-table .vxe-body--row:hover) {
  background-color: #e6f7ff;
}
</style>
