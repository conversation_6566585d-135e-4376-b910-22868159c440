<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="表名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tableName">
              <a-input v-model="model.tableName" placeholder="请输入表名"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="表类型: 0单表、1主表、2附表" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tableType">
              <a-input-number v-model="model.tableType" placeholder="请输入表类型: 0单表、1主表、2附表" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="表说明" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tableTxt">
              <a-input v-model="model.tableTxt" placeholder="请输入表说明"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否是树" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isTree">
              <a-input v-model="model.isTree" placeholder="请输入是否是树"  ></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'FlowOnlCgformHeadForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           tableName: [
              { required: true, message: '请输入表名!'},
           ],
           tableType: [
              { required: true, message: '请输入表类型: 0单表、1主表、2附表!'},
           ],
           tableTxt: [
              { required: true, message: '请输入表说明!'},
           ],
           isTree: [
              { required: true, message: '请输入是否是树!'},
           ],
        },
        url: {
          add: "/flowable/testOnlCgformHead/add",
          edit: "/flowable/testOnlCgformHead/edit",
          queryById: "/flowable/testOnlCgformHead/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>