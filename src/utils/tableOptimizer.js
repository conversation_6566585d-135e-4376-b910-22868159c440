/**
 * 表格性能优化工具类
 */

import { debounce, throttle } from 'lodash';

export class TableOptimizer {
  constructor(options = {}) {
    this.options = {
      batchSize: 50,
      debounceDelay: 100,
      useWebWorker: false,
      ...options
    };
    
    this.worker = null;
    this.initWebWorker();
  }
  
  // 初始化 Web Worker
  initWebWorker() {
    if (this.options.useWebWorker && typeof Worker !== 'undefined') {
      try {
        this.worker = new Worker('/src/utils/dataProcessWorker.js');
      } catch (error) {
        console.warn('Web Worker 初始化失败，将使用主线程处理:', error);
      }
    }
  }
  
  // 优化的数据处理函数
  processDataSource(items) {
    if (!Array.isArray(items)) return items;
    
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      
      // 直接修改原对象，避免创建新对象
      if (item.uploadDate == null) {
        item.uploadDate = '—';
      }
      if (item.filePage == null) {
        item.filePage = '—';
      }
      
      // 递归处理 children（如果存在）
      if (item.children && Array.isArray(item.children)) {
        this.processDataSource(item.children);
      }
    }
    
    return items;
  }
  
  // 使用 Web Worker 处理数据
  processDataWithWorker(data, options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.worker) {
        // 降级到主线程处理
        resolve(this.processDataSource(data));
        return;
      }
      
      const handleMessage = (e) => {
        const { type, data: result, error, progress } = e.data;
        
        switch (type) {
          case 'success':
            this.worker.removeEventListener('message', handleMessage);
            resolve(result);
            break;
          case 'error':
            this.worker.removeEventListener('message', handleMessage);
            reject(new Error(error));
            break;
          case 'progress':
            if (options.onProgress) {
              options.onProgress(progress);
            }
            break;
        }
      };
      
      this.worker.addEventListener('message', handleMessage);
      this.worker.postMessage({
        type: 'PROCESS_DATA',
        data: data,
        options: { batchSize: this.options.batchSize }
      });
    });
  }
  
  // 分批处理数据，避免阻塞UI
  async processDataInBatches(dataSource, onProgress) {
    const batchSize = this.options.batchSize;
    let processedCount = 0;
    
    for (let i = 0; i < dataSource.length; i += batchSize) {
      const batch = dataSource.slice(i, i + batchSize);
      
      // 处理当前批次
      for (const item of batch) {
        if (item.archives) {
          this.processDataSource(item.archives);
        }
      }
      
      processedCount += batch.length;
      
      // 报告进度
      if (onProgress) {
        onProgress({
          processed: processedCount,
          total: dataSource.length,
          percentage: Math.round((processedCount / dataSource.length) * 100)
        });
      }
      
      // 让出控制权给浏览器，避免阻塞UI
      await new Promise(resolve => requestAnimationFrame(resolve));
    }
    
    return dataSource;
  }
  
  // 防抖的展开行处理
  createDebouncedExpandHandler(handler, delay = this.options.debounceDelay) {
    return debounce(handler, delay);
  }
  
  // 节流的滚动处理
  createThrottledScrollHandler(handler, delay = 16) {
    return throttle(handler, delay);
  }
  
  // 虚拟滚动计算
  calculateVirtualScrollItems(items, containerHeight, itemHeight, scrollTop) {
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(startIndex + visibleCount + 5, items.length); // 多渲染5个作为缓冲
    
    return {
      visibleItems: items.slice(startIndex, endIndex),
      startIndex,
      endIndex,
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    };
  }
  
  // 清理资源
  destroy() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
  }
}

// 创建单例实例
export const tableOptimizer = new TableOptimizer({
  batchSize: 50,
  debounceDelay: 100,
  useWebWorker: true
});

// 导出便捷方法
export const processDataSource = (items) => tableOptimizer.processDataSource(items);
export const processDataInBatches = (dataSource, onProgress) => 
  tableOptimizer.processDataInBatches(dataSource, onProgress);
export const createDebouncedExpandHandler = (handler, delay) => 
  tableOptimizer.createDebouncedExpandHandler(handler, delay);
