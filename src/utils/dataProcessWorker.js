// Web Worker 用于处理大量数据，避免阻塞主线程

// 优化的数据处理函数
function processDataSource(items) {
  if (!Array.isArray(items)) return items;
  
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    
    // 直接修改原对象，避免创建新对象
    if (item.uploadDate == null) {
      item.uploadDate = '—';
    }
    if (item.filePage == null) {
      item.filePage = '—';
    }
    
    // 递归处理 children（如果存在）
    if (item.children && Array.isArray(item.children)) {
      processDataSource(item.children);
    }
  }
  
  return items;
}

// 分批处理大数据集
function processBatchData(data, batchSize = 100) {
  const results = [];
  
  for (let i = 0; i < data.length; i += batchSize) {
    const batch = data.slice(i, i + batchSize);
    const processedBatch = processDataSource(batch);
    results.push(...processedBatch);
    
    // 每处理一批数据后，发送进度更新
    self.postMessage({
      type: 'progress',
      progress: Math.min((i + batchSize) / data.length * 100, 100),
      processedCount: Math.min(i + batchSize, data.length),
      totalCount: data.length
    });
  }
  
  return results;
}

// 监听主线程消息
self.onmessage = function(e) {
  const { type, data, options = {} } = e.data;
  
  try {
    switch (type) {
      case 'PROCESS_DATA':
        const processedData = processBatchData(data, options.batchSize);
        self.postMessage({
          type: 'success',
          data: processedData
        });
        break;
        
      case 'PROCESS_SINGLE_ITEM':
        const processedItem = processDataSource([data])[0];
        self.postMessage({
          type: 'success',
          data: processedItem
        });
        break;
        
      default:
        self.postMessage({
          type: 'error',
          error: `Unknown message type: ${type}`
        });
    }
  } catch (error) {
    self.postMessage({
      type: 'error',
      error: error.message
    });
  }
};
