.page-title {
  font-size: 18px;
  color: #464c5b;
  line-height: 35px;
  padding-bottom: 5px;
  font-weight: 400;
  //border-bottom: 1px solid #e5e5e5;
}
.content-title {
  font-size: 18px;
  color: #464c5b;
  line-height: 35px;
  padding-bottom: 12px;
  margin-bottom: 12px;
  min-height: 52px;
  font-weight: 400;
  border-bottom: 1px solid #e5e5e5;
}

.content-action {
  float: right;
}

.content-action + .page-title {
  //padding-top: 55px;
}
.page-header{
  //margin-top: 65px;
  background: #fff;
  padding: 16px 32px 0;
  border-bottom: 1px solid #e8e8e8;
  .breadcrumb{
    margin-bottom: 16px;
  }
  .detail{
    display: flex;
    .row {
      display: flex;
      width: 100%;
    }
    .main{
      width: 100%;
      flex: 0 1 auto;
      .title{
        flex: auto;
        font-size: 20px;
        font-weight: 500;
        color: rgba(0,0,0,.85);
        margin-bottom: 16px;
      }
      .logo{
        width: 28px;
        height: 28px;
        border-radius: 4px;
        margin-right: 16px;
      }
      .content{
        margin-bottom: 16px;
        flex: auto;
      }
      .extra{
        flex: 0 1 auto;
        margin-left: 88px;
        min-width: 242px;
        text-align: right;
      }
      .action{
        margin-left: 56px;
        min-width: 266px;
        flex: 0 1 auto;
        text-align: right;
      }
    }
  }
}
.page-header-none{
  padding: 0;
  border-bottom: none;
}

.wrapper-main {
  margin: 24px;
  padding: 24px 0 12px 24px;
  background: rgb(255, 255, 255);
}

.wrapper-content {
  width: 100%;
  padding-right: 24px;
  transition: all 368ms;
  .action{
    padding-bottom: 12px;
    line-height: 3;
  }
}

.layout-content {
}

.page-search {
  margin-bottom: 16px;
}

.has-header-content .wrapper-content {
  padding: 80px 20px 0 276px;
  margin-top: 0;
  height: 100%;
}

.has-header-content .layout-content {
  box-shadow: 0 2px 3px 0 rgba(0, 0, 0, .047);
  padding: 5px 30px 15px 30px;
}

.wrapper-content .data-content {
  margin-top: 30px;
}

.layout-content .content-header {
  height: 56px;
  z-index: 5;
  width: 100%;
  line-height: 56px;
  overflow: hidden;
  border-bottom: 1px solid #e5e5e5;
  margin-bottom: 35px;
}

.layout-content .content-header h1:first-child {
  cursor: pointer;
  color: @primary-color;
}

.layout-content .content-header h1 {
  font-size: 14px;
  float: left;
  line-height: 56px;
  font-weight: 400;
}

.layout-copy {
  text-align: center;
  padding: 10px 0 20px;
  color: #9ea7b4;
}

