body {
  //overflow: auto; //解决发布界面不能滚动问题jky
}
.ant-layout{
  background: #f5f5f5;
}
.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.flex {
  display: flex;
}

//Text
.muted {
  color: rgba(0, 0, 0, 0.45);
  //font-weight: 400;
}

.text-grey {
  color: grey;
}

.text-default {
  color: #333;
}

.text-primary {
  color: @primary-color;
}

.text-info {
  color: @info-color;
}

.text-success {
  color: @success-color;
}

.text-processing {
  color: @processing-color;
}

.text-error {
  color: @error-color;
}

.text-highlight {
  color: @highlight-color;
}

.text-warning {
  color: @warning-color;
}

.text-normal {
  color: @normal-color;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

//Input
.ant-input-lg {
  font-size: 14px;
}

.ant-select-lg {
  font-size: 14px;
}

//Button
.action-btn {
  margin-top: 12px;

  .cancel-text {
    margin-right: 12px;
  }

  button {
    margin-right: 4px;
  }
}

.group-btn, .submit-set {
  .cancel-text {
    margin-right: 12px;
  }

  button {
    margin-right: 4px;
  }
}

.middle-btn {
  min-width: 66px;
  //min-height: 42px;
  //min-width: 90px;
  //height: 35px !important;
}

.ant-btn-lg {
  font-size: 14px;
}

.disabled-btn {
  background-color: #d9d9d9 !important;
  border-color: #d9d9d9 !important;
  color: #fff !important;
}

//Form
.ant-form-horizontal {
  .ant-form-item-with-help {
    margin-bottom: 12px;
  }
}

//Table
.ant-table-wrapper {
  clear: both;
}

.table-list {
  .item {
    display: inline-block;

    &.row-img {
      img {
        width: 85px;
        height: 85px;
      }
    }

    &.row-title {
      margin-left: 12px;
      vertical-align: middle;
      width: 75%;

      .title {
        margin-bottom: 0;

        &.title-name {
          padding: 5px 24px 5px 5px;
          color: @primary-color;
        }
      }
    }
  }

  .edit-content {
    .prefix {
      position: absolute;
      line-height: 2.3;
    }

    .editable-cell {
      width: 115px;
    }

    &.price {
      .editable-cell-input-wrapper, .editable-cell-text-wrapper {
        padding-left: 15px;
      }
    }
  }
}

//Drowdown
.ant-dropdown-menu-item {
  color: grey;
}

.field-right-menu {
  width: 240px;

  .ant-dropdown-menu-item {
    min-height: 36px;
    padding: 8px 16px;
    position: relative;

    .menu-action {
      position: absolute;
      right: 16px;
      top: 8px;
      font-size: 12px;
      color: @primary-color;
    }

    .menu-desc {
      padding-left: 22px;
      font-size: 12px;
    }

    .menu-item-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

//Select
.ant-select {
  min-width: 100px;
}

.wangEditor-txt {
  h1, h2, h3, h4, h5, h6 {
    font-weight: bold;
  }
}

//Modal
.ant-modal {
  top: 50px;
}

//Badge

.badge-blue {
  .ant-badge-status-dot {
    background-color: #1890ff;
  }
}

.badge-red {
  .ant-badge-status-dot {
    background-color: #f5222d;
  }
}

.badge-orange {
  .ant-badge-status-dot {
    background-color: #ff9900;
  }
}

.badge-green {
  .ant-badge-status-dot {
    background-color: #52c41a;
  }
}

.badge-brown {
  .ant-badge-status-dot {
    background-color: #2fbdb3;
  }
}

.badge-purple {
  .ant-badge-status-dot {
    background-color: #797ec9;
  }
}

.scroll-modal {
  .ant-modal-body {
    padding-bottom: 24px;
    height: 70vh;
  }

  .modal-content {
    padding: 0 24px;
    height: 100%;
  }

  .modal-footer {
    position: fixed;
    bottom: 13vh;
    background: #FFF;
    margin-bottom: 0;
  }
}

.task-detail-modal {
  width: 1200px;

  &.ant-modal {
    padding-bottom: 0;
  }

  .ant-modal-content {
    .ant-modal-body {
      padding: 0;
    }

  }
}

.hidden {
  display: none;
}

.project-navigation {
  border-bottom: 1px solid #D9D9D9;
  z-index: 1;
  background-color: #F5F5F5;
  transition: right 218ms ease;
  position: fixed;
  width: 100%;
}

.project-navigation .ivu-breadcrumb-item-separator {
  color: #383838;
}

.project-navigation .ivu-breadcrumb > span:last-child {
  font-weight: normal;
}

.project-navigation .project-nav-header {
  position: absolute;
  z-index: 2;
  left: 0;
  height: 50px;
  margin: 0;
  padding: 15px;

  .nav-title {
    font-size: 15px;
    margin-right: 12px;
  }

  .actions {
    cursor: pointer;
    margin-right: 12px;
  }
}

.project-navigation .nav-underscore {
  position: relative;
  height: 50px;
  border: none;
  margin-bottom: 0;
}

.project-navigation .nav-body {
  position: relative;
  white-space: nowrap;
  z-index: 1;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.project-navigation .nav-body .nav-wrapper.nav > li {
  z-index: 2;
  padding: 0 20px;
}

.project-navigation .nav-underscore > li {
  list-style: none;
  float: left;
}

.project-navigation .nav-underscore > li a {
  display: inline-block;
  position: relative;
  height: 50px;
  margin: 0;
  padding: 15px 0;
  color: #383838;
  font-size: 15px;
  font-weight: normal;
}

.project-navigation .nav-body .nav-wrapper.nav > li .app[data-app=tasks] {
  position: relative;
  float: left;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.project-navigation .nav-underscore > li > a:hover {
  background-color: inherit;
}

.project-navigation .nav-underscore > li > a:after {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 3px;
  width: 100%;
  border-bottom: 0 solid #3da8f5;
  content: '';
  opacity: 0;
}

.project-navigation .nav-underscore > li > a:hover:after, .project-navigation .nav-underscore > .actives > a:after {
  border-width: 3px;
  opacity: 1;
}

.simple {
  .project-navigation {
    background: hsla(0, 0%, 100%, .95);
  }
}
