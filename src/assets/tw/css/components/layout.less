#layout {
  height: 100vh;

  .ant-layout-header {
    background: #fff;
    padding: 0;
    line-height: 85px;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 5;
    //box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
    box-shadow: 0 0 8px 0 rgba(0,0,0,.1);
    ul {
      border-bottom: none;
    }
  }

  .logo {
    height: 32px;
    padding: 16px;
    text-align: center;
    box-sizing: content-box;
    transition: all .2s;
    width: 224px;
    float: left;
    line-height: 1.4;
    background: initial;
    color: #FFF;
    cursor: pointer;
    .logo-img {
      width: 30px;
      position: absolute;
      left: 35px;
      top: 15px;
      transition: all .2s;
    }

    .title {
      color: inherit;
      font-size: 20px;
      position: relative;
      .version {
        color: inherit;
        position: absolute;
        right: -25px;
        font-size: 14px;
      }
    }
  }

  .right-menu {
    position: absolute;
    right: 12px;
    top: 0;
    height: 100%;
    line-height: 66px;
    color: #FFF;
    display: flex;

    .action {
      cursor: pointer;
      //display: inline-block;
      transition: all .2s;
      padding: 0 6px;
      height: 65px;

      .anticon {
        font-size: 16px;
        //vertical-align: middle;
        color: #FFF;
        //color: rgba(0, 0, 0, .65);
      }

      .action-item {
        padding: 0 12px;
        height: 100%;
        position: relative;

        &.user-info {
          padding-left: 52px;

          .ant-avatar {
            position: absolute;
            left: 12px;
            top: 18px;
          }
        }
      }

      &:hover {
        background: rgba(0, 0, 0, .08);
        //background: #e6f7ff;
      }

      &.action-avatar {
        padding: 0;
      }

      &.action-organization {
        //min-width: 200px;
        text-align: center;
      }
    }
  }

  .ant-layout-sider {
    z-index: 2;
    overflow: auto;
    width: 256px !important;
    max-width: 256px !important;
    height: 100vh;
    position: fixed;
    left: 0;
    box-shadow: 2px 0 8px 0 rgba(29, 35, 41, .05);

    &.ant-layout-sider-collapsed {
      width: 80px !important;
      max-width: 80px !important;

      .ant-layout-sider-trigger {
        width: 80px !important;
      }

      .logo {
        .title {
          display: none;
        }
      }
    }

    .ant-layout-sider-trigger {
      width: 256px !important;
    }

  }

  .maint-content{
    .ant-layout-content{
      border-top: 1px solid #e8e8e8;
    }
  }

  .ant-layout-footer {
    margin: 0 6px;
    padding: 0 50px 12px 50px;
  }

  .ant-layout-header.collapsed{
    .logo{
      width: 48px;
      .logo-img{
        left: 27px;
      }
      .title{
        display: none;
      }
    }
  }

  &.layout-light {
    .ant-layout-header {
      ul {
        //background-color: #2b83f9;
        //background-image: linear-gradient(143deg,#2945cb 20%,#2b83f9 81%,#3a9dff);
        //background-image: linear-gradient(143deg, #2945cb 20%, #1890ff 81%, #3a9dff);
        background-image: linear-gradient(143deg, #e12f3f 20%, #f79275 81%, #FFC107);
        border-bottom: none;

        li {
          border-bottom: none;

          span, i {
            color: #FFF;
            //opacity: .69;
          }

          &:hover {
            span, i {
              opacity: 1;
            }
          }

          &.ant-menu-item-selected, &.ant-menu-item-active {
            color: #1890ff;
            background: rgba(0, 0, 0, 0.08);
          }
        }
      }
    }

    .ant-layout-sider {
      background: #FFF;
      .ant-layout-sider-children{
        .ant-menu-inline, .ant-menu-vertical, .ant-menu-vertical-left {
          border-right: none;
        }
      }
    }

    .logo {
      .title {
        color: inherit;

        .version {
          color: inherit;
        }
      }
    }

    .ant-layout-sider-trigger {
      background: #FFF;
      color: inherit;
    }
  }

  &.layout-dark {
    .ant-layout-header {
      ul {
        background-color: #fff;
        background-image: none;
      }
    }

    .logo {
      background: #002140;
      border-bottom: 1px solid #002140;
      box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
    }

    .ant-layout-sider{
      box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
    }

    .right-menu {
      color: initial;

      .action {
        .anticon {
          color: initial;
        }

        .action-item {
          &.user-info {
            .ant-avatar {
            }
          }
        }

        &:hover {
          background: rgba(0,0,0,.025);
          //background: #e6f7ff;
        }

        &.action-avatar {
        }

        &.action-organization {
        }
      }
    }

  }

  header {
    .ant-menu {
      //-webkit-box-shadow: 0 1px 4px rgba(0,21,41,.08);
      //box-shadow: 0 1px 4px rgba(0,21,41,.08);
    }
  }

  &.hide {
    .ant-layout-sider {
      display: none;
    }

    .main-content {
      padding-left: 0 !important;
    }
  }

  .trigger {
    font-size: 18px;
    line-height: 64px;
    padding: 0 24px;
    cursor: pointer;
    transition: color .2s;

    &:hover {
      color: #1890ff;
    }

  }
}

.middle-menu {
  padding: 4px 0;

  .ant-dropdown-menu-item-divider, .ant-dropdown-menu-submenu-title-divider {
    margin: 4px 0;
  }

  .ant-dropdown-menu-item {
    width: 160px;
  }

  &.organization-menu {
    .ant-dropdown-menu-item {
      width: 100%;
    }
  }
}

.__vuescroll .__rail-is-vertical {
  z-index: 3;
}
