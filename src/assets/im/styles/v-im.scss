@charset "UTF-8";

@import 'theme';

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
}

html {
  font-size: 62.5%;
  height: 100%;
}

body {
  font-size: 1.4rem;
  height: 100%;
}

.v-im {
  width: 100%;
  height: 100%;
  display: flex;

  background-color: $color-main;
  box-shadow: $box-shadow 2px 2px 5px;
}

.pull-right {
  float: right;
}

.text-center {
  text-align: center;
}




*:focus {
  outline: none;
}

.ivu-input {
  border: 0 !important;
  border-radius: 0 !important;
}
