@charset "UTF-8";
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
}

html {
  font-size: 62.5%;
  height: 100%;
  overflow: hidden;
}

body {
  font-size: 1.4rem;
  height: 100%;
  overflow: hidden;
}

body > div {
  height: 100%;
}

.v-im {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  background-color: #2590c2;
  box-shadow: #aaaaaa 2px 2px 5px;
}

::-webkit-scrollbar {
  width: 6px;
  height: 1px;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 2px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #535353;
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px transparent;
  border-radius: 2px;
  background: #ededed;
}

/*# sourceMappingURL=v-im.cssss.map */
