// 设置滚动条样式
// scrollbarWidth：滚动条宽度
// trackColor：轨道颜色
// thumbColor：滑块颜色
setScrollbar(scrollbarWidth, trackColor = #EBEEF5, thumbColor = #909399) {
  // 修改滚动条下面的宽度
  &::-webkit-scrollbar {
    width: scrollbarWidth;
  }
  // 修改滚动条的下面的样式
  &::-webkit-scrollbar-track {
    background-color: trackColor;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }
  // 修改滑块
  &::-webkit-scrollbar-thumb {
    background-color: thumbColor;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }
}
// 设置文字过长显示省略号
// line：在当前行显示省略号
setEllipsis(line) {
  display: -webkit-box;
  overflow: hidden;
  white-space: wrap;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical; /* -webkit-box-orient 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
  -webkit-line-clamp: line; /* -webkit-line-clamp用来限制在一个块元素显示的文本的行数 */
}