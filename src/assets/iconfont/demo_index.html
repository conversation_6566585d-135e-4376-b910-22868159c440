<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i2/O1CN01ZyAlrn1MwaMhqz36G_!!6000000001499-73-tps-64-64.ico" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01EYTRnJ297D6vehehJ_!!6000000008020-55-tps-64-64.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1782818" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe7de;</span>
                <div class="name">table</div>
                <div class="code-name">&amp;#xe7de;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">拆分行</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">拆分列</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">拆分</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ce;</span>
                <div class="name">行追加</div>
                <div class="code-name">&amp;#xe6ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6cf;</span>
                <div class="name">列追加</div>
                <div class="code-name">&amp;#xe6cf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8a2;</span>
                <div class="name">插入行</div>
                <div class="code-name">&amp;#xe8a2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8a4;</span>
                <div class="name">插入列</div>
                <div class="code-name">&amp;#xe8a4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66a;</span>
                <div class="name">上下合并</div>
                <div class="code-name">&amp;#xe66a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66b;</span>
                <div class="name">左右合并</div>
                <div class="code-name">&amp;#xe66b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">treeselect</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66e;</span>
                <div class="name">Barcode</div>
                <div class="code-name">&amp;#xe66e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">bar-code</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe692;</span>
                <div class="name">Dialog</div>
                <div class="code-name">&amp;#xe692;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b5;</span>
                <div class="name">Organization Chart</div>
                <div class="code-name">&amp;#xe6b5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe697;</span>
                <div class="name">级联</div>
                <div class="code-name">&amp;#xe697;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">Color picker</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75f;</span>
                <div class="name">省市区</div>
                <div class="code-name">&amp;#xe75f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fb;</span>
                <div class="name">文本</div>
                <div class="code-name">&amp;#xe6fb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8fb;</span>
                <div class="name">link</div>
                <div class="code-name">&amp;#xe8fb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">警告</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68e;</span>
                <div class="name">alert</div>
                <div class="code-name">&amp;#xe68e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">gitee</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">Logo GitHub</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe82a;</span>
                <div class="name">dialog</div>
                <div class="code-name">&amp;#xe82a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68a;</span>
                <div class="name">地图</div>
                <div class="code-name">&amp;#xe68a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bd;</span>
                <div class="name">编辑器</div>
                <div class="code-name">&amp;#xe7bd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b3;</span>
                <div class="name">divider</div>
                <div class="code-name">&amp;#xe6b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">按钮</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">按钮</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe705;</span>
                <div class="name">按钮</div>
                <div class="code-name">&amp;#xe705;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">加号</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">workbench_personal center_secondary password</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe822;</span>
                <div class="name">lock</div>
                <div class="code-name">&amp;#xe822;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">HTML</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">时间</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60f;</span>
                <div class="name">chart-area</div>
                <div class="code-name">&amp;#xe60f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">时间范围</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe629;</span>
                <div class="name">多选项</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b2;</span>
                <div class="name">日期</div>
                <div class="code-name">&amp;#xe7b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fe;</span>
                <div class="name">编辑器</div>
                <div class="code-name">&amp;#xe7fe;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe620;</span>
                <div class="name">滑块</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe802;</span>
                <div class="name">表格</div>
                <div class="code-name">&amp;#xe802;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7dc;</span>
                <div class="name">stream list</div>
                <div class="code-name">&amp;#xe7dc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">input</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62f;</span>
                <div class="name">开关 关</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe829;</span>
                <div class="name">评分</div>
                <div class="code-name">&amp;#xe829;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe624;</span>
                <div class="name">附 件</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe621;</span>
                <div class="name">html</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">编辑器</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">textarea</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">定位</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69d;</span>
                <div class="name">栅格</div>
                <div class="code-name">&amp;#xe69d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe73e;</span>
                <div class="name">分割线</div>
                <div class="code-name">&amp;#xe73e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe73b;</span>
                <div class="name">日期范围</div>
                <div class="code-name">&amp;#xe73b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63c;</span>
                <div class="name">下 拉</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">单选</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">chart-line</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a7;</span>
                <div class="name">计数器</div>
                <div class="code-name">&amp;#xe6a7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b4;</span>
                <div class="name">tab</div>
                <div class="code-name">&amp;#xe6b4;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1636125596282') format('woff2'),
       url('iconfont.woff?t=1636125596282') format('woff'),
       url('iconfont.ttf?t=1636125596282') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-table_layout"></span>
            <div class="name">
              table
            </div>
            <div class="code-name">.icon-table_layout
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chaifenhang"></span>
            <div class="name">
              拆分行
            </div>
            <div class="code-name">.icon-chaifenhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chaifenlie"></span>
            <div class="name">
              拆分列
            </div>
            <div class="code-name">.icon-chaifenlie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chaifen"></span>
            <div class="name">
              拆分
            </div>
            <div class="code-name">.icon-chaifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuijiahang"></span>
            <div class="name">
              行追加
            </div>
            <div class="code-name">.icon-zhuijiahang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuijialie"></span>
            <div class="name">
              列追加
            </div>
            <div class="code-name">.icon-zhuijialie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-charuhang"></span>
            <div class="name">
              插入行
            </div>
            <div class="code-name">.icon-charuhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-charulie"></span>
            <div class="name">
              插入列
            </div>
            <div class="code-name">.icon-charulie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shangxiahebing"></span>
            <div class="name">
              上下合并
            </div>
            <div class="code-name">.icon-shangxiahebing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zuoyouhebing"></span>
            <div class="name">
              左右合并
            </div>
            <div class="code-name">.icon-zuoyouhebing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-treeselect"></span>
            <div class="name">
              treeselect
            </div>
            <div class="code-name">.icon-treeselect
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-barcode2"></span>
            <div class="name">
              Barcode
            </div>
            <div class="code-name">.icon-barcode2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-barcode"></span>
            <div class="name">
              bar-code
            </div>
            <div class="code-name">.icon-barcode
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dialog"></span>
            <div class="name">
              Dialog
            </div>
            <div class="code-name">.icon-dialog
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-address"></span>
            <div class="name">
              Organization Chart
            </div>
            <div class="code-name">.icon-address
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cascader"></span>
            <div class="name">
              级联
            </div>
            <div class="code-name">.icon-cascader
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-colorpicker"></span>
            <div class="name">
              Color picker
            </div>
            <div class="code-name">.icon-colorpicker
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shengshiqu"></span>
            <div class="name">
              省市区
            </div>
            <div class="code-name">.icon-shengshiqu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-text"></span>
            <div class="name">
              文本
            </div>
            <div class="code-name">.icon-text
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-link"></span>
            <div class="name">
              link
            </div>
            <div class="code-name">.icon-link
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-alert"></span>
            <div class="name">
              警告
            </div>
            <div class="code-name">.icon-alert
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-alert1"></span>
            <div class="name">
              alert
            </div>
            <div class="code-name">.icon-alert1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gitee"></span>
            <div class="name">
              gitee
            </div>
            <div class="code-name">.icon-gitee
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-github"></span>
            <div class="name">
              Logo GitHub
            </div>
            <div class="code-name">.icon-github
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondialog"></span>
            <div class="name">
              dialog
            </div>
            <div class="code-name">.icondialog
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_map"></span>
            <div class="name">
              地图
            </div>
            <div class="code-name">.icon_map
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbianjiqi"></span>
            <div class="name">
              编辑器
            </div>
            <div class="code-name">.iconbianjiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondivider"></span>
            <div class="name">
              divider
            </div>
            <div class="code-name">.icondivider
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconanniu"></span>
            <div class="name">
              按钮
            </div>
            <div class="code-name">.iconanniu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-button"></span>
            <div class="name">
              按钮
            </div>
            <div class="code-name">.icon-button
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-button1"></span>
            <div class="name">
              按钮
            </div>
            <div class="code-name">.icon-button1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconadd"></span>
            <div class="name">
              加号
            </div>
            <div class="code-name">.iconadd
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-password"></span>
            <div class="name">
              workbench_personal center_secondary password
            </div>
            <div class="code-name">.icon-password
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lock"></span>
            <div class="name">
              lock
            </div>
            <div class="code-name">.icon-lock
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-html"></span>
            <div class="name">
              HTML
            </div>
            <div class="code-name">.icon-html
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-time"></span>
            <div class="name">
              时间
            </div>
            <div class="code-name">.icon-time
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chart1"></span>
            <div class="name">
              chart-area
            </div>
            <div class="code-name">.icon-chart1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-timerange"></span>
            <div class="name">
              时间范围
            </div>
            <div class="code-name">.icon-timerange
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-checkbox"></span>
            <div class="name">
              多选项
            </div>
            <div class="code-name">.icon-checkbox
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-date"></span>
            <div class="name">
              日期
            </div>
            <div class="code-name">.icon-date
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-setting"></span>
            <div class="name">
              编辑器
            </div>
            <div class="code-name">.icon-setting
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-slider"></span>
            <div class="name">
              滑块
            </div>
            <div class="code-name">.icon-slider
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-table"></span>
            <div class="name">
              表格
            </div>
            <div class="code-name">.icon-table
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-list"></span>
            <div class="name">
              stream list
            </div>
            <div class="code-name">.icon-list
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-input"></span>
            <div class="name">
              input
            </div>
            <div class="code-name">.icon-input
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-switch"></span>
            <div class="name">
              开关 关
            </div>
            <div class="code-name">.icon-switch
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rate"></span>
            <div class="name">
              评分
            </div>
            <div class="code-name">.icon-rate
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-att"></span>
            <div class="name">
              附 件
            </div>
            <div class="code-name">.icon-att
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-doc"></span>
            <div class="name">
              html
            </div>
            <div class="code-name">.icon-doc
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-editor"></span>
            <div class="name">
              编辑器
            </div>
            <div class="code-name">.icon-editor
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-textarea"></span>
            <div class="name">
              textarea
            </div>
            <div class="code-name">.icon-textarea
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-location"></span>
            <div class="name">
              定位
            </div>
            <div class="code-name">.icon-location
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-col"></span>
            <div class="name">
              栅格
            </div>
            <div class="code-name">.icon-col
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-divider"></span>
            <div class="name">
              分割线
            </div>
            <div class="code-name">.icon-divider
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daterange"></span>
            <div class="name">
              日期范围
            </div>
            <div class="code-name">.icon-daterange
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-select"></span>
            <div class="name">
              下 拉
            </div>
            <div class="code-name">.icon-select
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-radio"></span>
            <div class="name">
              单选
            </div>
            <div class="code-name">.icon-radio
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chart"></span>
            <div class="name">
              chart-line
            </div>
            <div class="code-name">.icon-chart
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-inputNumber"></span>
            <div class="name">
              计数器
            </div>
            <div class="code-name">.icon-inputNumber
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tab"></span>
            <div class="name">
              tab
            </div>
            <div class="code-name">.icon-tab
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont iconxxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-table_layout"></use>
                </svg>
                <div class="name">table</div>
                <div class="code-name">#icon-table_layout</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chaifenhang"></use>
                </svg>
                <div class="name">拆分行</div>
                <div class="code-name">#icon-chaifenhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chaifenlie"></use>
                </svg>
                <div class="name">拆分列</div>
                <div class="code-name">#icon-chaifenlie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chaifen"></use>
                </svg>
                <div class="name">拆分</div>
                <div class="code-name">#icon-chaifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuijiahang"></use>
                </svg>
                <div class="name">行追加</div>
                <div class="code-name">#icon-zhuijiahang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuijialie"></use>
                </svg>
                <div class="name">列追加</div>
                <div class="code-name">#icon-zhuijialie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-charuhang"></use>
                </svg>
                <div class="name">插入行</div>
                <div class="code-name">#icon-charuhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-charulie"></use>
                </svg>
                <div class="name">插入列</div>
                <div class="code-name">#icon-charulie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangxiahebing"></use>
                </svg>
                <div class="name">上下合并</div>
                <div class="code-name">#icon-shangxiahebing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zuoyouhebing"></use>
                </svg>
                <div class="name">左右合并</div>
                <div class="code-name">#icon-zuoyouhebing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-treeselect"></use>
                </svg>
                <div class="name">treeselect</div>
                <div class="code-name">#icon-treeselect</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-barcode2"></use>
                </svg>
                <div class="name">Barcode</div>
                <div class="code-name">#icon-barcode2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-barcode"></use>
                </svg>
                <div class="name">bar-code</div>
                <div class="code-name">#icon-barcode</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dialog"></use>
                </svg>
                <div class="name">Dialog</div>
                <div class="code-name">#icon-dialog</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-address"></use>
                </svg>
                <div class="name">Organization Chart</div>
                <div class="code-name">#icon-address</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cascader"></use>
                </svg>
                <div class="name">级联</div>
                <div class="code-name">#icon-cascader</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-colorpicker"></use>
                </svg>
                <div class="name">Color picker</div>
                <div class="code-name">#icon-colorpicker</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shengshiqu"></use>
                </svg>
                <div class="name">省市区</div>
                <div class="code-name">#icon-shengshiqu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-text"></use>
                </svg>
                <div class="name">文本</div>
                <div class="code-name">#icon-text</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-link"></use>
                </svg>
                <div class="name">link</div>
                <div class="code-name">#icon-link</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-alert"></use>
                </svg>
                <div class="name">警告</div>
                <div class="code-name">#icon-alert</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-alert1"></use>
                </svg>
                <div class="name">alert</div>
                <div class="code-name">#icon-alert1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gitee"></use>
                </svg>
                <div class="name">gitee</div>
                <div class="code-name">#icon-gitee</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-github"></use>
                </svg>
                <div class="name">Logo GitHub</div>
                <div class="code-name">#icon-github</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondialog"></use>
                </svg>
                <div class="name">dialog</div>
                <div class="code-name">#icondialog</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_map"></use>
                </svg>
                <div class="name">地图</div>
                <div class="code-name">#icon_map</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbianjiqi"></use>
                </svg>
                <div class="name">编辑器</div>
                <div class="code-name">#iconbianjiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondivider"></use>
                </svg>
                <div class="name">divider</div>
                <div class="code-name">#icondivider</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconanniu"></use>
                </svg>
                <div class="name">按钮</div>
                <div class="code-name">#iconanniu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-button"></use>
                </svg>
                <div class="name">按钮</div>
                <div class="code-name">#icon-button</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-button1"></use>
                </svg>
                <div class="name">按钮</div>
                <div class="code-name">#icon-button1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconadd"></use>
                </svg>
                <div class="name">加号</div>
                <div class="code-name">#iconadd</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-password"></use>
                </svg>
                <div class="name">workbench_personal center_secondary password</div>
                <div class="code-name">#icon-password</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lock"></use>
                </svg>
                <div class="name">lock</div>
                <div class="code-name">#icon-lock</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-html"></use>
                </svg>
                <div class="name">HTML</div>
                <div class="code-name">#icon-html</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-time"></use>
                </svg>
                <div class="name">时间</div>
                <div class="code-name">#icon-time</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chart1"></use>
                </svg>
                <div class="name">chart-area</div>
                <div class="code-name">#icon-chart1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-timerange"></use>
                </svg>
                <div class="name">时间范围</div>
                <div class="code-name">#icon-timerange</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-checkbox"></use>
                </svg>
                <div class="name">多选项</div>
                <div class="code-name">#icon-checkbox</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-date"></use>
                </svg>
                <div class="name">日期</div>
                <div class="code-name">#icon-date</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-setting"></use>
                </svg>
                <div class="name">编辑器</div>
                <div class="code-name">#icon-setting</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-slider"></use>
                </svg>
                <div class="name">滑块</div>
                <div class="code-name">#icon-slider</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-table"></use>
                </svg>
                <div class="name">表格</div>
                <div class="code-name">#icon-table</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-list"></use>
                </svg>
                <div class="name">stream list</div>
                <div class="code-name">#icon-list</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-input"></use>
                </svg>
                <div class="name">input</div>
                <div class="code-name">#icon-input</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-switch"></use>
                </svg>
                <div class="name">开关 关</div>
                <div class="code-name">#icon-switch</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rate"></use>
                </svg>
                <div class="name">评分</div>
                <div class="code-name">#icon-rate</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-att"></use>
                </svg>
                <div class="name">附 件</div>
                <div class="code-name">#icon-att</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-doc"></use>
                </svg>
                <div class="name">html</div>
                <div class="code-name">#icon-doc</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-editor"></use>
                </svg>
                <div class="name">编辑器</div>
                <div class="code-name">#icon-editor</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-textarea"></use>
                </svg>
                <div class="name">textarea</div>
                <div class="code-name">#icon-textarea</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-location"></use>
                </svg>
                <div class="name">定位</div>
                <div class="code-name">#icon-location</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-col"></use>
                </svg>
                <div class="name">栅格</div>
                <div class="code-name">#icon-col</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-divider"></use>
                </svg>
                <div class="name">分割线</div>
                <div class="code-name">#icon-divider</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daterange"></use>
                </svg>
                <div class="name">日期范围</div>
                <div class="code-name">#icon-daterange</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-select"></use>
                </svg>
                <div class="name">下 拉</div>
                <div class="code-name">#icon-select</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-radio"></use>
                </svg>
                <div class="name">单选</div>
                <div class="code-name">#icon-radio</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chart"></use>
                </svg>
                <div class="name">chart-line</div>
                <div class="code-name">#icon-chart</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-inputNumber"></use>
                </svg>
                <div class="name">计数器</div>
                <div class="code-name">#icon-inputNumber</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tab"></use>
                </svg>
                <div class="name">tab</div>
                <div class="code-name">#icon-tab</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
