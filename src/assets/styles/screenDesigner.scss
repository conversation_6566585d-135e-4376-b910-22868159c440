.layout {
  width: 100%;
  height: 100%;
  background: #242a30;
  color: #fff;

  .layout-container {
    width: 100%;
    height: calc(100vh - 40px);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    overflow: hidden;
    .layout-middle {
      // display: flex;
      position: relative;
      //width: calc(100% - 445px);
      height: 100%;
      background-color: rgb(36, 42, 48);
      box-sizing: border-box;
      -webkit-box-sizing: border-box;
      border: 1px solid rgb(36, 42, 48);
      align-items: center;
      vertical-align: middle;
      text-align: center;
    }
    .layout-right {
      width: 300px;
    }

    ::v-deep .el-tabs--border-card {
      border: 0;
      .el-tabs__header {
        background: transparent;
        .el-tabs__nav {
          width: 100%;
          .el-tabs__item {
            background-color: #242f3b;
            border: 0px;
            font-size: 12px;
            width: 50%;
            .icon {
              margin-right: 4px;
            }
          }

          .el-tabs__item.is-active {
            background-color: #31455d;
          }
        }
      }

      .el-tabs__content {
        background-color: #242a30;
        height: calc(100vh - 80px);
        overflow-x: hidden;
        overflow-y: auto;
        .el-tab-pane {
          color: #bfcbd9;
        }

        &::-webkit-scrollbar {
          width: 5px;
          height: 14px;
        }

        &::-webkit-scrollbar-track,
        &::-webkit-scrollbar-thumb {
          border-radius: 1px;
          border: 0 solid transparent;
        }

        &::-webkit-scrollbar-track-piece {
          /*修改滚动条的背景和圆角*/
          background: #29405c;
          -webkit-border-radius: 7px;
        }

        &::-webkit-scrollbar-track {
          box-shadow: 1px 1px 5px rgba(116, 148, 170, 0.5) inset;
        }

        &::-webkit-scrollbar-thumb {
          min-height: 20px;
          background-clip: content-box;
          box-shadow: 0 0 0 5px rgba(116, 148, 170, 0.5) inset;
        }

        &::-webkit-scrollbar-corner {
          background: transparent;
        }

        /*修改垂直滚动条的样式*/
        &::-webkit-scrollbar-thumb:vertical {
          background-color: #00113a;
          -webkit-border-radius: 7px;
        }

        /*修改水平滚动条的样式*/
        &::-webkit-scrollbar-thumb:horizontal {
          background-color: #00113a;
          -webkit-border-radius: 7px;
        }
      }
    }
  }
}

