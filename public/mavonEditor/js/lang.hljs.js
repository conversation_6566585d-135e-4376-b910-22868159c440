export default {
	'1c': '1c',
	abnf: 'abnf',
	accesslog: 'accesslog',
	actionscript: 'actionscript',
	as: 'actionscript',
	ada: 'ada',
	apache: 'apache',
	apacheconf: 'apache',
	applescript: 'applescript',
	osascript: 'applescript',
	arduino: 'arduino',
	armasm: 'armasm',
	arm: 'armasm',
	asciidoc: 'asciidoc',
	adoc: 'asciidoc',
	aspectj: 'aspectj',
	autohotkey: 'autohotkey',
	ahk: 'autohotkey',
	autoit: 'autoit',
	avrasm: 'avrasm',
	awk: 'awk',
	axapta: 'axapta',
	bash: 'bash',
	sh: 'bash',
	zsh: 'bash',
	basic: 'basic',
	bnf: 'bnf',
	brainfuck: 'brainfuck',
	bf: 'brainfuck',
	cal: 'cal',
	capnproto: 'capnproto',
	capnp: 'capnproto',
	ceylon: 'ceylon',
	clean: 'clean',
	icl: 'clean',
	dcl: 'clean',
	'clojure-repl': 'clojure-repl',
	clojure: 'clojure',
	clj: 'clojure',
	cmake: 'cmake',
	'cmake.in': 'cmake',
	coffeescript: 'coffeescript',
	coffee: 'coffeescript',
	cson: 'coffeescript',
	iced: 'coffeescript',
	coq: 'coq',
	cos: 'cos',
	cls: 'cos',
	cpp: 'cpp',
	c: 'cpp',
	cc: 'cpp',
	h: 'cpp',
	'c++': 'cpp',
	'h++': 'cpp',
	hpp: 'cpp',
	crmsh: 'crmsh',
	crm: 'crmsh',
	pcmk: 'crmsh',
	crystal: 'crystal',
	cr: 'crystal',
	cs: 'cs',
	csharp: 'cs',
	csp: 'csp',
	css: 'css',
	d: 'd',
	dart: 'dart',
	delphi: 'delphi',
	dpr: 'delphi',
	dfm: 'delphi',
	pas: 'delphi',
	pascal: 'delphi',
	freepascal: 'delphi',
	lazarus: 'delphi',
	lpr: 'delphi',
	lfm: 'delphi',
	diff: 'diff',
	patch: 'diff',
	django: 'django',
	jinja: 'django',
	dns: 'dns',
	bind: 'dns',
	zone: 'dns',
	dockerfile: 'dockerfile',
	docker: 'dockerfile',
	dos: 'dos',
	bat: 'dos',
	cmd: 'dos',
	dsconfig: 'dsconfig',
	dts: 'dts',
	dust: 'dust',
	dst: 'dust',
	ebnf: 'ebnf',
	elixir: 'elixir',
	elm: 'elm',
	erb: 'erb',
	'erlang-repl': 'erlang-repl',
	erlang: 'erlang',
	erl: 'erlang',
	excel: 'excel',
	xlsx: 'excel',
	xls: 'excel',
	fix: 'fix',
	flix: 'flix',
	fortran: 'fortran',
	f90: 'fortran',
	f95: 'fortran',
	fsharp: 'fsharp',
	fs: 'fsharp',
	gams: 'gams',
	gms: 'gams',
	gauss: 'gauss',
	gss: 'gauss',
	gcode: 'gcode',
	nc: 'gcode',
	gherkin: 'gherkin',
	feature: 'gherkin',
	glsl: 'glsl',
	go: 'go',
	golang: 'go',
	golo: 'golo',
	gradle: 'gradle',
	groovy: 'groovy',
	haml: 'haml',
	handlebars: 'handlebars',
	hbs: 'handlebars',
	'html.hbs': 'handlebars',
	'html.handlebars': 'handlebars',
	haskell: 'haskell',
	hs: 'haskell',
	haxe: 'haxe',
	hx: 'haxe',
	hsp: 'hsp',
	htmlbars: 'htmlbars',
	http: 'http',
	https: 'http',
	hy: 'hy',
	hylang: 'hy',
	inform7: 'inform7',
	i7: 'inform7',
	ini: 'ini',
	toml: 'ini',
	irpf90: 'irpf90',
	java: 'java',
	jsp: 'java',
	javascript: 'javascript',
	js: 'javascript',
	jsx: 'javascript',
	'jboss-cli': 'jboss-cli',
	'wildfly-cli': 'jboss-cli',
	json: 'json',
	'julia-repl': 'julia-repl',
	julia: 'julia',
	kotlin: 'kotlin',
	lasso: 'lasso',
	ls: 'livescript',
	lassoscript: 'lasso',
	ldif: 'ldif',
	leaf: 'leaf',
	less: 'less',
	lisp: 'lisp',
	livecodeserver: 'livecodeserver',
	livescript: 'livescript',
	llvm: 'llvm',
	lsl: 'lsl',
	lua: 'lua',
	makefile: 'makefile',
	mk: 'makefile',
	mak: 'makefile',
	markdown: 'markdown',
	md: 'markdown',
	mkdown: 'markdown',
	mkd: 'markdown',
	mathematica: 'mathematica',
	mma: 'mathematica',
	matlab: 'matlab',
	maxima: 'maxima',
	mel: 'mel',
	mercury: 'mercury',
	m: 'mercury',
	moo: 'mercury',
	mipsasm: 'mipsasm',
	mips: 'mipsasm',
	mizar: 'mizar',
	mojolicious: 'mojolicious',
	monkey: 'monkey',
	moonscript: 'moonscript',
	moon: 'moonscript',
	n1ql: 'n1ql',
	nginx: 'nginx',
	nginxconf: 'nginx',
	nimrod: 'nimrod',
	nim: 'nimrod',
	nix: 'nix',
	nixos: 'nix',
	nsis: 'nsis',
	objectivec: 'objectivec',
	mm: 'objectivec',
	objc: 'objectivec',
	'obj-c': 'objectivec',
	ocaml: 'ocaml',
	ml: 'sml',
	openscad: 'openscad',
	scad: 'openscad',
	oxygene: 'oxygene',
	parser3: 'parser3',
	perl: 'perl',
	pl: 'perl',
	pm: 'perl',
	pf: 'pf',
	'pf.conf': 'pf',
	php: 'php',
	php3: 'php',
	php4: 'php',
	php5: 'php',
	php6: 'php',
	pony: 'pony',
	powershell: 'powershell',
	ps: 'powershell',
	processing: 'processing',
	profile: 'profile',
	prolog: 'prolog',
	protobuf: 'protobuf',
	puppet: 'puppet',
	pp: 'puppet',
	purebasic: 'purebasic',
	pb: 'purebasic',
	pbi: 'purebasic',
	python: 'python',
	py: 'python',
	gyp: 'python',
	q: 'q',
	k: 'q',
	kdb: 'q',
	qml: 'qml',
	qt: 'qml',
	r: 'r',
	rib: 'rib',
	roboconf: 'roboconf',
	graph: 'roboconf',
	instances: 'roboconf',
	routeros: 'routeros',
	mikrotik: 'routeros',
	rsl: 'rsl',
	ruby: 'ruby',
	rb: 'ruby',
	gemspec: 'ruby',
	podspec: 'ruby',
	thor: 'ruby',
	irb: 'ruby',
	ruleslanguage: 'ruleslanguage',
	rust: 'rust',
	rs: 'rust',
	scala: 'scala',
	scheme: 'scheme',
	scilab: 'scilab',
	sci: 'scilab',
	scss: 'scss',
	shell: 'shell',
	console: 'shell',
	smali: 'smali',
	smalltalk: 'smalltalk',
	st: 'smalltalk',
	sml: 'sml',
	sqf: 'sqf',
	sql: 'sql',
	stan: 'stan',
	stata: 'stata',
	do: 'stata',
	ado: 'stata',
	step21: 'step21',
	p21: 'step21',
	step: 'step21',
	stp: 'step21',
	stylus: 'stylus',
	styl: 'stylus',
	subunit: 'subunit',
	swift: 'swift',
	taggerscript: 'taggerscript',
	tap: 'tap',
	tcl: 'tcl',
	tk: 'tcl',
	tex: 'tex',
	thrift: 'thrift',
	tp: 'tp',
	twig: 'twig',
	craftcms: 'twig',
	typescript: 'typescript',
	ts: 'typescript',
	vala: 'vala',
	vbnet: 'vbnet',
	vb: 'vbnet',
	'vbscript-html': 'vbscript-html',
	vbscript: 'vbscript',
	vbs: 'vbscript',
	verilog: 'verilog',
	v: 'verilog',
	sv: 'verilog',
	svh: 'verilog',
	vhdl: 'vhdl',
	vim: 'vim',
	x86asm: 'x86asm',
	xl: 'xl',
	tao: 'xl',
	xml: 'xml',
	html: 'xml',
	xhtml: 'xml',
	rss: 'xml',
	atom: 'xml',
	xjb: 'xml',
	xsd: 'xml',
	xsl: 'xml',
	plist: 'xml',
	xquery: 'xquery',
	xpath: 'xquery',
	xq: 'xquery',
	yaml: 'yaml',
	yml: 'yaml',
	YAML: 'yaml',
	zephir: 'zephir',
	zep: 'zephir'
}
