window.sheetSparkline = {
	"name": "Sparkline",
	"color": "",
	"config": {
		"merge": {
			"1_2": {
				"r": 1,
				"c": 2,
				"rs": 1,
				"cs": 2
			},
			"1_4": {
				"r": 1,
				"c": 4,
				"rs": 1,
				"cs": 2
			},
			"0_0": {
				"r": 0,
				"c": 0,
				"rs": 1,
				"cs": 6
			},
			"2_2": {
				"r": 2,
				"c": 2,
				"rs": 3,
				"cs": 2
			},
			"2_4": {
				"r": 2,
				"c": 4,
				"rs": 3,
				"cs": 2
			},
			"6_0": {
				"r": 6,
				"c": 0,
				"rs": 1,
				"cs": 5
			},
			"7_2": {
				"r": 7,
				"c": 2,
				"rs": 1,
				"cs": 2
			},
			"8_2": {
				"r": 8,
				"c": 2,
				"rs": 3,
				"cs": 2
			},
			"12_0": {
				"r": 12,
				"c": 0,
				"rs": 1,
				"cs": 5
			},
			"13_2": {
				"r": 13,
				"c": 2,
				"rs": 1,
				"cs": 3
			},
			"14_2": {
				"r": 14,
				"c": 2,
				"rs": 4,
				"cs": 3
			},
			"19_0": {
				"r": 19,
				"c": 0,
				"rs": 1,
				"cs": 5
			},
			"0_9": {
				"r": 0,
				"c": 9,
				"rs": 1,
				"cs": 5
			},
			"1_12": {
				"r": 1,
				"c": 12,
				"rs": 1,
				"cs": 2
			},
			"2_12": {
				"r": 2,
				"c": 12,
				"rs": 1,
				"cs": 2
			},
			"3_12": {
				"r": 3,
				"c": 12,
				"rs": 1,
				"cs": 2
			},
			"4_12": {
				"r": 4,
				"c": 12,
				"rs": 1,
				"cs": 2
			},
			"6_6": {
				"r": 6,
				"c": 6,
				"rs": 1,
				"cs": 8
			},
			"7_6": {
				"r": 7,
				"c": 6,
				"rs": 1,
				"cs": 2
			},
			"7_11": {
				"r": 7,
				"c": 11,
				"rs": 1,
				"cs": 3
			},
			"8_6": {
				"r": 8,
				"c": 6,
				"rs": 1,
				"cs": 2
			},
			"9_6": {
				"r": 9,
				"c": 6,
				"rs": 1,
				"cs": 2
			},
			"10_6": {
				"r": 10,
				"c": 6,
				"rs": 1,
				"cs": 2
			},
			"8_11": {
				"r": 8,
				"c": 11,
				"rs": 3,
				"cs": 3
			},
			"13_6": {
				"r": 13,
				"c": 6,
				"rs": 1,
				"cs": 7
			},
			"14_7": {
				"r": 14,
				"c": 7,
				"rs": 1,
				"cs": 2
			},
			"14_9": {
				"r": 14,
				"c": 9,
				"rs": 1,
				"cs": 2
			},
			"14_11": {
				"r": 14,
				"c": 11,
				"rs": 1,
				"cs": 2
			},
			"15_6": {
				"r": 15,
				"c": 6,
				"rs": 2,
				"cs": 1
			},
			"17_7": {
				"r": 17,
				"c": 7,
				"rs": 1,
				"cs": 2
			},
			"17_9": {
				"r": 17,
				"c": 9,
				"rs": 1,
				"cs": 2
			},
			"17_11": {
				"r": 17,
				"c": 11,
				"rs": 1,
				"cs": 2
			},
			"18_7": {
				"r": 18,
				"c": 7,
				"rs": 1,
				"cs": 2
			},
			"18_9": {
				"r": 18,
				"c": 9,
				"rs": 1,
				"cs": 2
			},
			"18_11": {
				"r": 18,
				"c": 11,
				"rs": 1,
				"cs": 2
			},
			"19_7": {
				"r": 19,
				"c": 7,
				"rs": 1,
				"cs": 2
			},
			"19_9": {
				"r": 19,
				"c": 9,
				"rs": 1,
				"cs": 2
			},
			"19_11": {
				"r": 19,
				"c": 11,
				"rs": 1,
				"cs": 2
			},
			"20_7": {
				"r": 20,
				"c": 7,
				"rs": 1,
				"cs": 2
			},
			"20_9": {
				"r": 20,
				"c": 9,
				"rs": 1,
				"cs": 2
			},
			"20_11": {
				"r": 20,
				"c": 11,
				"rs": 1,
				"cs": 2
			},
			"21_7": {
				"r": 21,
				"c": 7,
				"rs": 1,
				"cs": 2
			},
			"21_9": {
				"r": 21,
				"c": 9,
				"rs": 1,
				"cs": 2
			},
			"21_11": {
				"r": 21,
				"c": 11,
				"rs": 1,
				"cs": 2
			},
			"15_7": {
				"r": 15,
				"c": 7,
				"rs": 2,
				"cs": 7
			},
			"20_0": {
				"r": 20,
				"c": 0,
				"rs": 1,
				"cs": 5
			},
			"21_3": {
				"r": 21,
				"c": 3,
				"rs": 1,
				"cs": 2
			},
			"22_3": {
				"r": 22,
				"c": 3,
				"rs": 3,
				"cs": 2
			},
			"27_2": {
				"r": 27,
				"c": 2,
				"rs": 1,
				"cs": 3
			}
		},
		"rowlen": {
			"0": 29,
			"1": 20,
			"2": 20,
			"3": 20,
			"4": 20,
			"6": 29,
			"7": 20,
			"8": 20,
			"9": 20,
			"10": 20,
			"12": 29,
			"13": 29,
			"14": 20,
			"15": 20,
			"16": 26,
			"17": 20,
			"18": 20,
			"19": 29,
			"20": 29,
			"21": 20,
			"22": 20,
			"23": 20,
			"24": 20,
			"25": 20,
			"27": 100,
			"28": 20,
			"29": 20,
			"30": 20,
			"31": 20,
			"32": 20,
			"33": 20,
			"34": 26,
			"35": 20,
			"36": 20,
			"37": 20,
			"38": 20,
			"39": 20,
			"40": 20,
			"41": 20,
			"42": 20,
			"43": 20,
			"44": 20,
			"45": 20,
			"46": 20,
			"47": 20,
			"48": 20,
			"49": 20,
			"50": 20,
			"51": 20,
			"52": 20,
			"53": 20,
			"54": 20,
			"55": 20,
			"56": 20,
			"57": 20
		},
		"columnlen": {
			"0": 101,
			"2": 131,
			"3": 30,
			"4": 90
		},
		"borderInfo": [{
			"rangeType": "cell",
			"value": {
				"row_index": 7,
				"col_index": 6,
				"b": {
					"style": 13,
					"color": "rgb(0, 0, 0)"
				}
			}
		}, {
			"rangeType": "cell",
			"value": {
				"row_index": 7,
				"col_index": 7,
				"b": {
					"style": 13,
					"color": "rgb(0, 0, 0)"
				}
			}
		}, {
			"rangeType": "cell",
			"value": {
				"row_index": 7,
				"col_index": 8,
				"b": {
					"style": 13,
					"color": "rgb(0, 0, 0)"
				}
			}
		}, {
			"rangeType": "cell",
			"value": {
				"row_index": 7,
				"col_index": 9,
				"b": {
					"style": 13,
					"color": "rgb(0, 0, 0)"
				}
			}
		}, {
			"rangeType": "cell",
			"value": {
				"row_index": 7,
				"col_index": 10,
				"b": {
					"style": 13,
					"color": "rgb(0, 0, 0)"
				}
			}
		}, {
			"rangeType": "cell",
			"value": {
				"row_index": 7,
				"col_index": 11,
				"b": {
					"style": 13,
					"color": "rgb(0, 0, 0)"
				}
			}
		}, {
			"rangeType": "cell",
			"value": {
				"row_index": 7,
				"col_index": 12,
				"b": {
					"style": 13,
					"color": "rgb(0, 0, 0)"
				}
			}
		}, {
			"rangeType": "cell",
			"value": {
				"row_index": 7,
				"col_index": 13,
				"b": {
					"style": 13,
					"color": "rgb(0, 0, 0)"
				}
			}
		}]
	},
	"index": "4",
	"chart": [],
	"status": 0,
	"order": "4",
	"column": 18,
	"row": 36,
	"celldata": [{
		"r": 0,
		"c": 0,
		"v": {
			"v": "The company revenue in 2014",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "The company revenue in 2014",
			"mc": {
				"r": 0,
				"c": 0,
				"rs": 1,
				"cs": 6
			},
			"fs": "14",
			"ht": "0",
			"vt": "0"
		}
	}, {
		"r": 0,
		"c": 1,
		"v": {
			"mc": {
				"r": 0,
				"c": 0
			},
			"fs": "14",
			"ht": "0",
			"vt": "0"
		}
	}, {
		"r": 0,
		"c": 2,
		"v": {
			"mc": {
				"r": 0,
				"c": 0
			},
			"fs": "14",
			"ht": "0",
			"vt": "0"
		}
	}, {
		"r": 0,
		"c": 3,
		"v": {
			"mc": {
				"r": 0,
				"c": 0
			},
			"fs": "14",
			"ht": "0",
			"vt": "0"
		}
	}, {
		"r": 0,
		"c": 4,
		"v": {
			"mc": {
				"r": 0,
				"c": 0
			},
			"fs": "14",
			"ht": "0",
			"vt": "0"
		}
	}, {
		"r": 0,
		"c": 5,
		"v": {
			"mc": {
				"r": 0,
				"c": 0
			},
			"fs": "14",
			"ht": "0",
			"vt": "0"
		}
	}, {
		"r": 0,
		"c": 9,
		"v": {
			"v": "Mobile Phone Contrast",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Mobile Phone Contrast",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 9,
			"fs": 16,
			"fc": "rgb(51, 51, 51)",
			"ht": 0,
			"vt": 0,
			"mc": {
				"r": 0,
				"c": 9,
				"rs": 1,
				"cs": 5
			}
		}
	}, {
		"r": 0,
		"c": 10,
		"v": {
			"mc": {
				"r": 0,
				"c": 9
			}
		}
	}, {
		"r": 0,
		"c": 11,
		"v": {
			"mc": {
				"r": 0,
				"c": 9
			}
		}
	}, {
		"r": 0,
		"c": 12,
		"v": {
			"mc": {
				"r": 0,
				"c": 9
			}
		}
	}, {
		"r": 0,
		"c": 13,
		"v": {
			"mc": {
				"r": 0,
				"c": 9
			}
		}
	}, {
		"r": 1,
		"c": 0,
		"v": {
			"m": "Month",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Month",
			"bg": "#f1c232",
			"fc": "#ffffff",
			"ht": "1",
			"vt": "0"
		}
	}, {
		"r": 1,
		"c": 1,
		"v": {
			"m": "Revenue",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Revenue",
			"bg": "#f1c232",
			"fc": "#ffffff",
			"ht": "1",
			"vt": "0"
		}
	}, {
		"r": 1,
		"c": 2,
		"v": {
			"m": "Diagram 1",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Diagram 1",
			"mc": {
				"r": 1,
				"c": 2,
				"rs": 1,
				"cs": 2
			},
			"bg": "#f1c232",
			"fc": "#ffffff",
			"ht": "1",
			"vt": "0"
		}
	}, {
		"r": 1,
		"c": 3,
		"v": {
			"mc": {
				"r": 1,
				"c": 2
			},
			"bg": "#f1c232",
			"fc": "#ffffff",
			"ht": "1",
			"vt": "0"
		}
	}, {
		"r": 1,
		"c": 4,
		"v": {
			"m": "Diagram 2",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Diagram 2",
			"mc": {
				"r": 1,
				"c": 4,
				"rs": 1,
				"cs": 2
			},
			"bg": "#f1c232",
			"fc": "#ffffff",
			"ht": "1",
			"vt": "0"
		}
	}, {
		"r": 1,
		"c": 5,
		"v": {
			"mc": {
				"r": 1,
				"c": 4
			},
			"bg": "#f1c232",
			"fc": "#ffffff",
			"ht": "1",
			"vt": "0"
		}
	}, {
		"r": 1,
		"c": 9,
		"v": {
			"v": null,
			"m": "",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": 0,
			"vt": 0
		}
	}, {
		"r": 1,
		"c": 10,
		"v": {
			"v": "Phone I",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Phone I",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": 0,
			"vt": 0
		}
	}, {
		"r": 1,
		"c": 11,
		"v": {
			"v": "Phone II",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Phone II",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": 0,
			"vt": 0
		}
	}, {
		"r": 1,
		"c": 12,
		"v": {
			"v": "Diagram",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Diagram",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": 0,
			"vt": 0,
			"mc": {
				"r": 1,
				"c": 12,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 1,
		"c": 13,
		"v": {
			"mc": {
				"r": 1,
				"c": 12
			}
		}
	}, {
		"r": 2,
		"c": 0,
		"v": {
			"m": "2014-02-01",
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"v": 41671
		}
	}, {
		"r": 2,
		"c": 1,
		"v": {
			"v": 30,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "30",
			"ht": "0",
			"vt": "0"
		}
	}, {
		"r": 2,
		"c": 2,
		"v": {
			"mc": {
				"r": 2,
				"c": 2,
				"rs": 3,
				"cs": 2
			},
			"f": "=LINESPLINES(B3:B5,'pink',4,'avg','yellow','red','green',3)",
			"spl": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Shape",
						"args": [0, [
							[0, 21],
							[0, 21],
							[80, 54],
							[159, 3]
						], "pink", null, 4]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 0, null, 159, null, null, "yellow"]
					},
					"2": {
						"id": 2,
						"type": "Circle",
						"args": [2, 80, 54, 3, null, "green", null]
					},
					"3": {
						"id": 3,
						"type": "Circle",
						"args": [3, 159, 3, 3, null, "red", null]
					}
				},
				"shapeseq": [0, 1, 2, 3],
				"offsetX": 0,
				"offsetY": 5,
				"pixelWidth": 162,
				"pixelHeight": 58
			}
		}
	}, {
		"r": 2,
		"c": 3,
		"v": {
			"mc": {
				"r": 2,
				"c": 2
			}
		}
	}, {
		"r": 2,
		"c": 4,
		"v": {
			"mc": {
				"r": 2,
				"c": 4,
				"rs": 3,
				"cs": 2
			},
			"f": "=COLUMNSPLINES(B3:B5,35,'red','green','auto','brown')",
			"spl": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 108, 1, 18, 29, "red", "red"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 54, 31, 18, 21, "green", "green"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 0, 20, 18, 10, "brown", "brown"]
					}
				},
				"shapeseq": [0, 1, 2],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 164,
				"pixelHeight": 63
			}
		}
	}, {
		"r": 2,
		"c": 5,
		"v": {
			"mc": {
				"r": 2,
				"c": 4
			}
		}
	}, {
		"r": 2,
		"c": 9,
		"v": {
			"v": "Size(inch)",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Size(inch)",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 2,
		"c": 10,
		"v": {
			"v": 5,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "5",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 2,
		"c": 11,
		"v": {
			"v": 3.7,
			"ct": {
				"fa": "0.0",
				"t": "n"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "3.7"
		}
	}, {
		"r": 2,
		"c": 12,
		"v": {
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 2,
				"c": 12,
				"rs": 1,
				"cs": 2
			},
			"f": "=STACKBARSPLINES(K3:L3)",
			"spl": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 0, 10, 107, 8, "#fc5c5c", "#fc5c5c"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 0, 0, 145, 8, "#fc5c5c", "#fc5c5c"]
					}
				},
				"shapeseq": [0, 1],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 147,
				"pixelHeight": 21
			},
			"ct": {
				"fa": "General",
				"t": "n"
			}
		}
	}, {
		"r": 2,
		"c": 13,
		"v": {
			"mc": {
				"r": 2,
				"c": 12
			}
		}
	}, {
		"r": 3,
		"c": 0,
		"v": {
			"m": "2014-03-01",
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"v": 41699
		}
	}, {
		"r": 3,
		"c": 1,
		"v": {
			"v": -60,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "-60",
			"ht": "0",
			"vt": "0"
		}
	}, {
		"r": 3,
		"c": 2,
		"v": {
			"mc": {
				"r": 2,
				"c": 2
			}
		}
	}, {
		"r": 3,
		"c": 3,
		"v": {
			"mc": {
				"r": 2,
				"c": 2
			}
		}
	}, {
		"r": 3,
		"c": 4,
		"v": {
			"mc": {
				"r": 2,
				"c": 4
			}
		}
	}, {
		"r": 3,
		"c": 5,
		"v": {
			"mc": {
				"r": 2,
				"c": 4
			}
		}
	}, {
		"r": 3,
		"c": 9,
		"v": {
			"v": "RAM(G)",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "RAM(G)",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 3,
		"c": 10,
		"v": {
			"v": 3,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "3",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 3,
		"c": 11,
		"v": {
			"v": 1,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "1",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 3,
		"c": 12,
		"v": {
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 3,
				"c": 12,
				"rs": 1,
				"cs": 2
			},
			"f": "=STACKBARSPLINES(K4:L4)",
			"spl": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 0, 10, 47, 8, "#fc5c5c", "#fc5c5c"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 0, 0, 145, 8, "#fc5c5c", "#fc5c5c"]
					}
				},
				"shapeseq": [0, 1],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 147,
				"pixelHeight": 21
			}
		}
	}, {
		"r": 3,
		"c": 13,
		"v": {
			"mc": {
				"r": 3,
				"c": 12
			}
		}
	}, {
		"r": 4,
		"c": 0,
		"v": {
			"m": "2014-04-01",
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"v": 41730
		}
	}, {
		"r": 4,
		"c": 1,
		"v": {
			"v": 80,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "80",
			"ht": "0",
			"vt": "0"
		}
	}, {
		"r": 4,
		"c": 2,
		"v": {
			"mc": {
				"r": 2,
				"c": 2
			}
		}
	}, {
		"r": 4,
		"c": 3,
		"v": {
			"mc": {
				"r": 2,
				"c": 2
			}
		}
	}, {
		"r": 4,
		"c": 4,
		"v": {
			"mc": {
				"r": 2,
				"c": 4
			}
		}
	}, {
		"r": 4,
		"c": 5,
		"v": {
			"mc": {
				"r": 2,
				"c": 4
			}
		}
	}, {
		"r": 4,
		"c": 9,
		"v": {
			"v": "Weight(g)",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Weight(g)",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 4,
		"c": 10,
		"v": {
			"v": 149,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "149",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 4,
		"c": 11,
		"v": {
			"v": 129,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "129",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 4,
		"c": 12,
		"v": {
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 4,
				"c": 12,
				"rs": 1,
				"cs": 2
			},
			"f": "=STACKBARSPLINES(K5:L5)",
			"spl": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 0, 10, 125, 8, "#fc5c5c", "#fc5c5c"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 0, 0, 145, 8, "#fc5c5c", "#fc5c5c"]
					}
				},
				"shapeseq": [0, 1],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 147,
				"pixelHeight": 21
			}
		}
	}, {
		"r": 4,
		"c": 13,
		"v": {
			"mc": {
				"r": 4,
				"c": 12
			}
		}
	}, {
		"r": 6,
		"c": 0,
		"v": {
			"v": "My Assets",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "My Assets",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 9,
			"fs": "14",
			"fc": "rgb(51, 51, 51)",
			"ht": 0,
			"vt": 0,
			"mc": {
				"r": 6,
				"c": 0,
				"rs": 1,
				"cs": 5
			}
		}
	}, {
		"r": 6,
		"c": 1,
		"v": {
			"mc": {
				"r": 6,
				"c": 0
			},
			"fs": "14"
		}
	}, {
		"r": 6,
		"c": 2,
		"v": {
			"mc": {
				"r": 6,
				"c": 0
			},
			"fs": "14"
		}
	}, {
		"r": 6,
		"c": 3,
		"v": {
			"mc": {
				"r": 6,
				"c": 0
			},
			"fs": "14"
		}
	}, {
		"r": 6,
		"c": 4,
		"v": {
			"mc": {
				"r": 6,
				"c": 0
			},
			"fs": "14"
		}
	}, {
		"r": 6,
		"c": 6,
		"v": {
			"v": "Checkbook Register",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Checkbook Register",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 9,
			"fs": 16,
			"fc": "rgb(51, 51, 51)",
			"ht": 0,
			"vt": 0,
			"mc": {
				"r": 6,
				"c": 6,
				"rs": 1,
				"cs": 8
			}
		}
	}, {
		"r": 6,
		"c": 7,
		"v": {
			"mc": {
				"r": 6,
				"c": 6
			}
		}
	}, {
		"r": 6,
		"c": 8,
		"v": {
			"mc": {
				"r": 6,
				"c": 6
			}
		}
	}, {
		"r": 6,
		"c": 9,
		"v": {
			"mc": {
				"r": 6,
				"c": 6
			}
		}
	}, {
		"r": 6,
		"c": 10,
		"v": {
			"mc": {
				"r": 6,
				"c": 6
			}
		}
	}, {
		"r": 6,
		"c": 11,
		"v": {
			"mc": {
				"r": 6,
				"c": 6
			}
		}
	}, {
		"r": 6,
		"c": 12,
		"v": {
			"mc": {
				"r": 6,
				"c": 6
			}
		}
	}, {
		"r": 6,
		"c": 13,
		"v": {
			"mc": {
				"r": 6,
				"c": 6
			}
		}
	}, {
		"r": 7,
		"c": 0,
		"v": {
			"v": "Asset Type",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Asset Type",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": "10",
			"fc": "rgb(255, 255, 255)",
			"ht": "1",
			"vt": "0"
		}
	}, {
		"r": 7,
		"c": 1,
		"v": {
			"v": "Amount",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Amount",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": "10",
			"fc": "rgb(255, 255, 255)",
			"ht": "1",
			"vt": "0"
		}
	}, {
		"r": 7,
		"c": 2,
		"v": {
			"v": "Diagram",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Diagram",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": "10",
			"fc": "rgb(255, 255, 255)",
			"ht": "1",
			"vt": "0",
			"mc": {
				"r": 7,
				"c": 2,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 7,
		"c": 3,
		"v": {
			"mc": {
				"r": 7,
				"c": 2
			},
			"fs": "10",
			"ht": "1",
			"vt": "0"
		}
	}, {
		"r": 7,
		"c": 4,
		"v": {
			"v": "Note",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Note",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": "10",
			"fc": "rgb(255, 255, 255)",
			"ht": "1",
			"vt": "0"
		}
	}, {
		"r": 7,
		"c": 6,
		"v": {
			"v": null,
			"m": "",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 7,
				"c": 6,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 7,
		"c": 7,
		"v": {
			"mc": {
				"r": 7,
				"c": 6
			}
		}
	}, {
		"r": 7,
		"c": 8,
		"v": {
			"v": "InitialValue",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "InitialValue",
			"bg": null,
			"bl": 1,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 7,
		"c": 9,
		"v": {
			"v": 815.25,
			"ct": {
				"fa": "0.00",
				"t": "n"
			},
			"m": "815.25",
			"bg": null,
			"bl": 1,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 7,
		"c": 10,
		"v": {
			"v": "Σ",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Σ",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 0,
			"vt": 1
		}
	}, {
		"r": 7,
		"c": 11,
		"v": {
			"v": null,
			"m": "",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 7,
				"c": 11,
				"rs": 1,
				"cs": 3
			}
		}
	}, {
		"r": 7,
		"c": 12,
		"v": {
			"mc": {
				"r": 7,
				"c": 11
			}
		}
	}, {
		"r": 7,
		"c": 13,
		"v": {
			"mc": {
				"r": 7,
				"c": 11
			}
		}
	}, {
		"r": 8,
		"c": 0,
		"v": {
			"v": "Savings",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Savings",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 8,
		"c": 1,
		"v": {
			"v": 25000,
			"ct": {
				"fa": "\"$\" #",
				"t": "n"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "$ 25000"
		}
	}, {
		"r": 8,
		"c": 2,
		"v": {
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 8,
				"c": 2,
				"rs": 3,
				"cs": 2
			},
			"f": "=PIESPLINES(B9:B11)",
			"spl": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "PieSlice",
						"args": [0, 31, 31, 31, 5.291103416572283, 6.283185307179586, null, "#5ab1ef"]
					},
					"1": {
						"id": 1,
						"type": "PieSlice",
						"args": [1, 31, 31, 31, 1.6534698176788385, 5.291103416572283, null, "#fc5c5c"]
					},
					"2": {
						"id": 2,
						"type": "PieSlice",
						"args": [2, 31, 31, 31, 0, 1.6534698176788385, null, "#2ec7c9"]
					}
				},
				"shapeseq": [0, 1, 2],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 162,
				"pixelHeight": 63
			}
		}
	}, {
		"r": 8,
		"c": 3,
		"v": {
			"mc": {
				"r": 8,
				"c": 2
			}
		}
	}, {
		"r": 8,
		"c": 4,
		"v": {
			"v": 0.2631578947368421,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "0.263157895",
			"bg": "rgb(145, 159, 129)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"f": "=B9/SUM(B9:B11)"
		}
	}, {
		"r": 8,
		"c": 5,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			}
		}
	}, {
		"r": 8,
		"c": 6,
		"v": {
			"v": "12/11/2012",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "12/11/2012",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 8,
				"c": 6,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 8,
		"c": 7,
		"v": {
			"mc": {
				"r": 8,
				"c": 6
			}
		}
	}, {
		"r": 8,
		"c": 8,
		"v": {
			"v": "CVS",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "CVS",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 8,
		"c": 9,
		"v": {
			"v": -200,
			"ct": {
				"fa": "0.00",
				"t": "n"
			},
			"m": "-200.00",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 8,
		"c": 10,
		"v": {
			"v": 615.25,
			"ct": {
				"fa": "0.00",
				"t": "n"
			},
			"m": "615.25",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 8,
		"c": 11,
		"v": {
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"f": "=BARSPLINES(J9:J11)",
			"spl": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 56, 42, 53, 19, "#97b552", "#97b552"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 110, 21, 108, 19, "#fc5c5c", "#fc5c5c"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 89, 0, 20, 19, "#97b552", "#97b552"]
					}
				},
				"shapeseq": [0, 1, 2],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 221,
				"pixelHeight": 63
			},
			"mc": {
				"r": 8,
				"c": 11,
				"rs": 3,
				"cs": 3
			}
		}
	}, {
		"r": 8,
		"c": 12,
		"v": {
			"mc": {
				"r": 8,
				"c": 11
			}
		}
	}, {
		"r": 8,
		"c": 13,
		"v": {
			"mc": {
				"r": 8,
				"c": 11
			}
		}
	}, {
		"r": 9,
		"c": 0,
		"v": {
			"v": "401k",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "401k",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 9,
		"c": 1,
		"v": {
			"v": 55000,
			"ct": {
				"fa": "\"$\" #",
				"t": "n"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "$ 55000"
		}
	}, {
		"r": 9,
		"c": 2,
		"v": {
			"mc": {
				"r": 8,
				"c": 2
			}
		}
	}, {
		"r": 9,
		"c": 3,
		"v": {
			"mc": {
				"r": 8,
				"c": 2
			}
		}
	}, {
		"r": 9,
		"c": 4,
		"v": {
			"v": 0.5789473684210527,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "0.578947368",
			"bg": "rgb(215, 145, 62)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"f": "=B10/SUM(B9:B11)"
		}
	}, {
		"r": 9,
		"c": 5,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			}
		}
	}, {
		"r": 9,
		"c": 6,
		"v": {
			"v": "12/12/2012",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "12/12/2012",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 9,
				"c": 6,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 9,
		"c": 7,
		"v": {
			"mc": {
				"r": 9,
				"c": 6
			}
		}
	}, {
		"r": 9,
		"c": 8,
		"v": {
			"v": "Bank",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Bank",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 9,
		"c": 9,
		"v": {
			"v": 1000.12,
			"ct": {
				"fa": "#,##0.00",
				"t": "n"
			},
			"m": "1,000.12",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 9,
		"c": 10,
		"v": {
			"v": 1615.37,
			"ct": {
				"fa": "#,##0.00",
				"t": "n"
			},
			"m": "1,615.37",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 9,
		"c": 11,
		"v": {
			"mc": {
				"r": 8,
				"c": 11
			}
		}
	}, {
		"r": 9,
		"c": 12,
		"v": {
			"mc": {
				"r": 8,
				"c": 11
			}
		}
	}, {
		"r": 9,
		"c": 13,
		"v": {
			"mc": {
				"r": 8,
				"c": 11
			}
		}
	}, {
		"r": 10,
		"c": 0,
		"v": {
			"v": "Stocks",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Stocks",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 10,
		"c": 1,
		"v": {
			"v": 15000,
			"ct": {
				"fa": "\"$\" #",
				"t": "n"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "$ 15000"
		}
	}, {
		"r": 10,
		"c": 2,
		"v": {
			"mc": {
				"r": 8,
				"c": 2
			}
		}
	}, {
		"r": 10,
		"c": 3,
		"v": {
			"mc": {
				"r": 8,
				"c": 2
			}
		}
	}, {
		"r": 10,
		"c": 4,
		"v": {
			"v": 0.15789473684210525,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "0.157894737",
			"bg": "rgb(206, 167, 34)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"f": "=B11/SUM(B9:B11)"
		}
	}, {
		"r": 10,
		"c": 5,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			}
		}
	}, {
		"r": 10,
		"c": 6,
		"v": {
			"v": "12/13/2012",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "12/13/2012",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 10,
				"c": 6,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 10,
		"c": 7,
		"v": {
			"mc": {
				"r": 10,
				"c": 6
			}
		}
	}, {
		"r": 10,
		"c": 8,
		"v": {
			"v": "Starbucks",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Starbucks",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 10,
		"c": 9,
		"v": {
			"v": -500.43,
			"ct": {
				"fa": "0.00",
				"t": "n"
			},
			"m": "-500.43",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 10,
		"c": 10,
		"v": {
			"v": 1114.94,
			"ct": {
				"fa": "#,##0.00",
				"t": "n"
			},
			"m": "1,114.94",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 10,
		"c": 11,
		"v": {
			"mc": {
				"r": 8,
				"c": 11
			}
		}
	}, {
		"r": 10,
		"c": 12,
		"v": {
			"mc": {
				"r": 8,
				"c": 11
			}
		}
	}, {
		"r": 10,
		"c": 13,
		"v": {
			"mc": {
				"r": 8,
				"c": 11
			}
		}
	}, {
		"r": 12,
		"c": 0,
		"v": {
			"v": "Sales by State",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Sales by State",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 9,
			"fs": "14",
			"fc": "rgb(51, 51, 51)",
			"ht": 0,
			"vt": 0,
			"mc": {
				"r": 12,
				"c": 0,
				"rs": 1,
				"cs": 5
			}
		}
	}, {
		"r": 12,
		"c": 1,
		"v": {
			"mc": {
				"r": 12,
				"c": 0
			},
			"fs": "14"
		}
	}, {
		"r": 12,
		"c": 2,
		"v": {
			"mc": {
				"r": 12,
				"c": 0
			},
			"fs": "14"
		}
	}, {
		"r": 12,
		"c": 3,
		"v": {
			"mc": {
				"r": 12,
				"c": 0
			},
			"fs": "14"
		}
	}, {
		"r": 12,
		"c": 4,
		"v": {
			"mc": {
				"r": 12,
				"c": 0
			},
			"fs": "14"
		}
	}, {
		"r": 13,
		"c": 0,
		"v": {
			"v": "State",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "State",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": "1",
			"vt": 1
		}
	}, {
		"r": 13,
		"c": 1,
		"v": {
			"v": "Sales",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Sales",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": "1",
			"vt": 1
		}
	}, {
		"r": 13,
		"c": 2,
		"v": {
			"v": "Diagram",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Diagram",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": "1",
			"vt": 1,
			"mc": {
				"r": 13,
				"c": 2,
				"rs": 1,
				"cs": 3
			}
		}
	}, {
		"r": 13,
		"c": 3,
		"v": {
			"mc": {
				"r": 13,
				"c": 2
			},
			"ht": "1"
		}
	}, {
		"r": 13,
		"c": 4,
		"v": {
			"mc": {
				"r": 13,
				"c": 2
			},
			"ht": "1"
		}
	}, {
		"r": 13,
		"c": 6,
		"v": {
			"v": "Student Grade Statistics",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Student Grade Statistics",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 9,
			"fs": 16,
			"fc": "rgb(51, 51, 51)",
			"ht": 0,
			"vt": 0,
			"mc": {
				"r": 13,
				"c": 6,
				"rs": 1,
				"cs": 7
			}
		}
	}, {
		"r": 13,
		"c": 7,
		"v": {
			"mc": {
				"r": 13,
				"c": 6
			}
		}
	}, {
		"r": 13,
		"c": 8,
		"v": {
			"mc": {
				"r": 13,
				"c": 6
			}
		}
	}, {
		"r": 13,
		"c": 9,
		"v": {
			"mc": {
				"r": 13,
				"c": 6
			}
		}
	}, {
		"r": 13,
		"c": 10,
		"v": {
			"mc": {
				"r": 13,
				"c": 6
			}
		}
	}, {
		"r": 13,
		"c": 11,
		"v": {
			"mc": {
				"r": 13,
				"c": 6
			}
		}
	}, {
		"r": 13,
		"c": 12,
		"v": {
			"mc": {
				"r": 13,
				"c": 6
			}
		}
	}, {
		"r": 13,
		"c": 13,
		"v": {
			"v": null,
			"m": "",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 14,
		"c": 0,
		"v": {
			"v": "Idaho",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Idaho",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 14,
		"c": 1,
		"v": {
			"v": 3500,
			"ct": {
				"fa": "\"$\" #",
				"t": "n"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "$ 3500"
		}
	}, {
		"r": 14,
		"c": 2,
		"v": {
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 14,
				"c": 2,
				"rs": 4,
				"cs": 3
			},
			"f": "=AREASPLINES(B15:B18)",
			"spl": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Shape",
						"args": [0, [
							[0, 87],
							[0, 61],
							[84, 0],
							[169, 87],
							[253, 35],
							[253, 87]
						], "#CCF3F4", "#CCF3F4", null]
					},
					"1": {
						"id": 1,
						"type": "Shape",
						"args": [1, [
							[0, 61],
							[0, 61],
							[84, 0],
							[169, 87],
							[253, 35]
						], "#2ec7c9", null, 1]
					}
				},
				"shapeseq": [0, 1],
				"offsetX": 0,
				"offsetY": 2,
				"pixelWidth": 253,
				"pixelHeight": 88
			}
		}
	}, {
		"r": 14,
		"c": 3,
		"v": {
			"mc": {
				"r": 14,
				"c": 2
			}
		}
	}, {
		"r": 14,
		"c": 4,
		"v": {
			"mc": {
				"r": 14,
				"c": 2
			}
		}
	}, {
		"r": 14,
		"c": 6,
		"v": {
			"v": "Name",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Name",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": 0,
			"vt": 0
		}
	}, {
		"r": 14,
		"c": 7,
		"v": {
			"v": "Chinese",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Chinese",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": 0,
			"vt": 0,
			"mc": {
				"r": 14,
				"c": 7,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 14,
		"c": 8,
		"v": {
			"mc": {
				"r": 14,
				"c": 7
			}
		}
	}, {
		"r": 14,
		"c": 9,
		"v": {
			"v": "Math",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Math",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": 0,
			"vt": 0,
			"mc": {
				"r": 14,
				"c": 9,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 14,
		"c": 10,
		"v": {
			"mc": {
				"r": 14,
				"c": 9
			}
		}
	}, {
		"r": 14,
		"c": 11,
		"v": {
			"v": "English",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "English",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": 0,
			"vt": 0,
			"mc": {
				"r": 14,
				"c": 11,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 14,
		"c": 12,
		"v": {
			"mc": {
				"r": 14,
				"c": 11
			}
		}
	}, {
		"r": 14,
		"c": 13,
		"v": {
			"v": "Total",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Total",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": 0,
			"vt": 0
		}
	}, {
		"r": 15,
		"c": 0,
		"v": {
			"v": "Montana",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Montana",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 15,
		"c": 1,
		"v": {
			"v": 7000,
			"ct": {
				"fa": "\"$\" #",
				"t": "n"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "$ 7000"
		}
	}, {
		"r": 15,
		"c": 2,
		"v": {
			"mc": {
				"r": 14,
				"c": 2
			}
		}
	}, {
		"r": 15,
		"c": 3,
		"v": {
			"mc": {
				"r": 14,
				"c": 2
			}
		}
	}, {
		"r": 15,
		"c": 4,
		"v": {
			"mc": {
				"r": 14,
				"c": 2
			}
		}
	}, {
		"r": 15,
		"c": 6,
		"v": {
			"v": null,
			"m": "",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 15,
				"c": 6,
				"rs": 2,
				"cs": 1
			}
		}
	}, {
		"r": 15,
		"c": 7,
		"v": {
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"f": "=TRISTATESPLINES(H18:N22,10)",
			"spl": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 476, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 462, 23, 3, 1, "#999", "#999"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 448, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"3": {
						"id": 3,
						"type": "Rect",
						"args": [3, 434, 23, 3, 1, "#999", "#999"]
					},
					"4": {
						"id": 4,
						"type": "Rect",
						"args": [4, 420, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"5": {
						"id": 5,
						"type": "Rect",
						"args": [5, 406, 23, 3, 1, "#999", "#999"]
					},
					"6": {
						"id": 6,
						"type": "Rect",
						"args": [6, 392, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"7": {
						"id": 7,
						"type": "Rect",
						"args": [7, 378, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"8": {
						"id": 8,
						"type": "Rect",
						"args": [8, 364, 23, 3, 1, "#999", "#999"]
					},
					"9": {
						"id": 9,
						"type": "Rect",
						"args": [9, 350, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"10": {
						"id": 10,
						"type": "Rect",
						"args": [10, 336, 23, 3, 1, "#999", "#999"]
					},
					"11": {
						"id": 11,
						"type": "Rect",
						"args": [11, 322, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"12": {
						"id": 12,
						"type": "Rect",
						"args": [12, 308, 23, 3, 1, "#999", "#999"]
					},
					"13": {
						"id": 13,
						"type": "Rect",
						"args": [13, 294, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"14": {
						"id": 14,
						"type": "Rect",
						"args": [14, 280, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"15": {
						"id": 15,
						"type": "Rect",
						"args": [15, 266, 23, 3, 1, "#999", "#999"]
					},
					"16": {
						"id": 16,
						"type": "Rect",
						"args": [16, 252, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"17": {
						"id": 17,
						"type": "Rect",
						"args": [17, 238, 23, 3, 1, "#999", "#999"]
					},
					"18": {
						"id": 18,
						"type": "Rect",
						"args": [18, 224, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"19": {
						"id": 19,
						"type": "Rect",
						"args": [19, 210, 23, 3, 1, "#999", "#999"]
					},
					"20": {
						"id": 20,
						"type": "Rect",
						"args": [20, 196, 24, 3, 22, "#97b552", "#97b552"]
					},
					"21": {
						"id": 21,
						"type": "Rect",
						"args": [21, 182, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"22": {
						"id": 22,
						"type": "Rect",
						"args": [22, 168, 23, 3, 1, "#999", "#999"]
					},
					"23": {
						"id": 23,
						"type": "Rect",
						"args": [23, 154, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"24": {
						"id": 24,
						"type": "Rect",
						"args": [24, 140, 23, 3, 1, "#999", "#999"]
					},
					"25": {
						"id": 25,
						"type": "Rect",
						"args": [25, 126, 24, 3, 22, "#97b552", "#97b552"]
					},
					"26": {
						"id": 26,
						"type": "Rect",
						"args": [26, 112, 23, 3, 1, "#999", "#999"]
					},
					"27": {
						"id": 27,
						"type": "Rect",
						"args": [27, 98, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"28": {
						"id": 28,
						"type": "Rect",
						"args": [28, 84, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"29": {
						"id": 29,
						"type": "Rect",
						"args": [29, 70, 23, 3, 1, "#999", "#999"]
					},
					"30": {
						"id": 30,
						"type": "Rect",
						"args": [30, 56, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"31": {
						"id": 31,
						"type": "Rect",
						"args": [31, 42, 23, 3, 1, "#999", "#999"]
					},
					"32": {
						"id": 32,
						"type": "Rect",
						"args": [32, 28, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"33": {
						"id": 33,
						"type": "Rect",
						"args": [33, 14, 23, 3, 1, "#999", "#999"]
					},
					"34": {
						"id": 34,
						"type": "Rect",
						"args": [34, 0, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					}
				},
				"shapeseq": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 517,
				"pixelHeight": 48
			},
			"mc": {
				"r": 15,
				"c": 7,
				"rs": 2,
				"cs": 7
			}
		}
	}, {
		"r": 15,
		"c": 8,
		"v": {
			"mc": {
				"r": 15,
				"c": 7
			}
		}
	}, {
		"r": 15,
		"c": 9,
		"v": {
			"mc": {
				"r": 15,
				"c": 7
			}
		}
	}, {
		"r": 15,
		"c": 10,
		"v": {
			"mc": {
				"r": 15,
				"c": 7
			}
		}
	}, {
		"r": 15,
		"c": 11,
		"v": {
			"mc": {
				"r": 15,
				"c": 7
			}
		}
	}, {
		"r": 15,
		"c": 12,
		"v": {
			"mc": {
				"r": 15,
				"c": 7
			}
		}
	}, {
		"r": 15,
		"c": 13,
		"v": {
			"mc": {
				"r": 15,
				"c": 7
			}
		}
	}, {
		"r": 16,
		"c": 0,
		"v": {
			"v": "Oregon",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Oregon",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 16,
		"c": 1,
		"v": {
			"v": 2000,
			"ct": {
				"fa": "\"$\" #",
				"t": "n"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "$ 2000"
		}
	}, {
		"r": 16,
		"c": 2,
		"v": {
			"mc": {
				"r": 14,
				"c": 2
			}
		}
	}, {
		"r": 16,
		"c": 3,
		"v": {
			"mc": {
				"r": 14,
				"c": 2
			}
		}
	}, {
		"r": 16,
		"c": 4,
		"v": {
			"mc": {
				"r": 14,
				"c": 2
			}
		}
	}, {
		"r": 16,
		"c": 6,
		"v": {
			"mc": {
				"r": 15,
				"c": 6
			}
		}
	}, {
		"r": 16,
		"c": 7,
		"v": {
			"mc": {
				"r": 15,
				"c": 7
			}
		}
	}, {
		"r": 16,
		"c": 8,
		"v": {
			"mc": {
				"r": 15,
				"c": 7
			}
		}
	}, {
		"r": 16,
		"c": 9,
		"v": {
			"mc": {
				"r": 15,
				"c": 7
			}
		}
	}, {
		"r": 16,
		"c": 10,
		"v": {
			"mc": {
				"r": 15,
				"c": 7
			}
		}
	}, {
		"r": 16,
		"c": 11,
		"v": {
			"mc": {
				"r": 15,
				"c": 7
			}
		}
	}, {
		"r": 16,
		"c": 12,
		"v": {
			"mc": {
				"r": 15,
				"c": 7
			}
		}
	}, {
		"r": 16,
		"c": 13,
		"v": {
			"mc": {
				"r": 15,
				"c": 7
			}
		}
	}, {
		"r": 17,
		"c": 0,
		"v": {
			"v": "Washington",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Washington",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 17,
		"c": 1,
		"v": {
			"v": 5000,
			"ct": {
				"fa": "\"$\" #",
				"t": "n"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "$ 5000"
		}
	}, {
		"r": 17,
		"c": 2,
		"v": {
			"mc": {
				"r": 14,
				"c": 2
			}
		}
	}, {
		"r": 17,
		"c": 3,
		"v": {
			"mc": {
				"r": 14,
				"c": 2
			}
		}
	}, {
		"r": 17,
		"c": 4,
		"v": {
			"mc": {
				"r": 14,
				"c": 2
			}
		}
	}, {
		"r": 17,
		"c": 6,
		"v": {
			"v": "Student 1",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Student 1",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 17,
		"c": 7,
		"v": {
			"v": 70,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "70",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 17,
				"c": 7,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 17,
		"c": 8,
		"v": {
			"mc": {
				"r": 17,
				"c": 7
			}
		}
	}, {
		"r": 17,
		"c": 9,
		"v": {
			"v": 90,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "90",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 17,
				"c": 9,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 17,
		"c": 10,
		"v": {
			"mc": {
				"r": 17,
				"c": 9
			}
		}
	}, {
		"r": 17,
		"c": 11,
		"v": {
			"v": 51,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "51",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 17,
				"c": 11,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 17,
		"c": 12,
		"v": {
			"mc": {
				"r": 17,
				"c": 11
			}
		}
	}, {
		"r": 17,
		"c": 13,
		"v": {
			"v": 211,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "211",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 18,
		"c": 6,
		"v": {
			"v": "Student 2",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Student 2",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 18,
		"c": 7,
		"v": {
			"v": 99,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "99",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 18,
				"c": 7,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 18,
		"c": 8,
		"v": {
			"mc": {
				"r": 18,
				"c": 7
			}
		}
	}, {
		"r": 18,
		"c": 9,
		"v": {
			"v": -59,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 18,
				"c": 9,
				"rs": 1,
				"cs": 2
			},
			"m": "-59"
		}
	}, {
		"r": 18,
		"c": 10,
		"v": {
			"mc": {
				"r": 18,
				"c": 9
			}
		}
	}, {
		"r": 18,
		"c": 11,
		"v": {
			"v": 63,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "63",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 18,
				"c": 11,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 18,
		"c": 12,
		"v": {
			"mc": {
				"r": 18,
				"c": 11
			}
		}
	}, {
		"r": 18,
		"c": 13,
		"v": {
			"v": 221,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "221",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 19,
		"c": 0,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 9,
			"fs": "14",
			"fc": "rgb(51, 51, 51)",
			"ht": 0,
			"vt": 0,
			"mc": {
				"r": 19,
				"c": 0,
				"rs": 1,
				"cs": 5
			}
		}
	}, {
		"r": 19,
		"c": 1,
		"v": {
			"mc": {
				"r": 19,
				"c": 0
			},
			"fs": "14"
		}
	}, {
		"r": 19,
		"c": 2,
		"v": {
			"mc": {
				"r": 19,
				"c": 0
			},
			"fs": "14"
		}
	}, {
		"r": 19,
		"c": 3,
		"v": {
			"mc": {
				"r": 19,
				"c": 0
			},
			"fs": "14"
		}
	}, {
		"r": 19,
		"c": 4,
		"v": {
			"mc": {
				"r": 19,
				"c": 0
			},
			"fs": "14"
		}
	}, {
		"r": 19,
		"c": 6,
		"v": {
			"v": "Student 3",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Student 3",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 19,
		"c": 7,
		"v": {
			"v": -90,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 19,
				"c": 7,
				"rs": 1,
				"cs": 2
			},
			"m": "-90"
		}
	}, {
		"r": 19,
		"c": 8,
		"v": {
			"mc": {
				"r": 19,
				"c": 7
			}
		}
	}, {
		"r": 19,
		"c": 9,
		"v": {
			"v": 128,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "128",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 19,
				"c": 9,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 19,
		"c": 10,
		"v": {
			"mc": {
				"r": 19,
				"c": 9
			}
		}
	}, {
		"r": 19,
		"c": 11,
		"v": {
			"v": 74,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "74",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 19,
				"c": 11,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 19,
		"c": 12,
		"v": {
			"mc": {
				"r": 19,
				"c": 11
			}
		}
	}, {
		"r": 19,
		"c": 13,
		"v": {
			"v": 291,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "291",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 20,
		"c": 0,
		"v": {
			"v": "Employee KPI",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Employee KPI",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 9,
			"fs": 16,
			"fc": "rgb(51, 51, 51)",
			"ht": 0,
			"vt": 0,
			"mc": {
				"r": 20,
				"c": 0,
				"rs": 1,
				"cs": 5
			}
		}
	}, {
		"r": 20,
		"c": 1,
		"v": {
			"mc": {
				"r": 20,
				"c": 0
			}
		}
	}, {
		"r": 20,
		"c": 2,
		"v": {
			"mc": {
				"r": 20,
				"c": 0
			}
		}
	}, {
		"r": 20,
		"c": 3,
		"v": {
			"mc": {
				"r": 20,
				"c": 0
			}
		}
	}, {
		"r": 20,
		"c": 4,
		"v": {
			"mc": {
				"r": 20,
				"c": 0
			}
		}
	}, {
		"r": 20,
		"c": 6,
		"v": {
			"v": "Student 4",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Student 4",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 20,
		"c": 7,
		"v": {
			"v": 93,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "93",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 20,
				"c": 7,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 20,
		"c": 8,
		"v": {
			"mc": {
				"r": 20,
				"c": 7
			}
		}
	}, {
		"r": 20,
		"c": 9,
		"v": {
			"v": 61,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "61",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 20,
				"c": 9,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 20,
		"c": 10,
		"v": {
			"mc": {
				"r": 20,
				"c": 9
			}
		}
	}, {
		"r": 20,
		"c": 11,
		"v": {
			"v": 53,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "53",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 20,
				"c": 11,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 20,
		"c": 12,
		"v": {
			"mc": {
				"r": 20,
				"c": 11
			}
		}
	}, {
		"r": 20,
		"c": 13,
		"v": {
			"v": 207,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "207",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 21,
		"c": 0,
		"v": {
			"v": "Name",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Name",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": 0,
			"vt": 0
		}
	}, {
		"r": 21,
		"c": 1,
		"v": {
			"v": "Forecast",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Forecast",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": 0,
			"vt": 0
		}
	}, {
		"r": 21,
		"c": 2,
		"v": {
			"v": "Actuality",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Actuality",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": 0,
			"vt": 0
		}
	}, {
		"r": 21,
		"c": 3,
		"v": {
			"v": "Diagram",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Diagram",
			"bg": "rgb(255, 192, 0)",
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(255, 255, 255)",
			"ht": 0,
			"vt": 0,
			"mc": {
				"r": 21,
				"c": 3,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 21,
		"c": 4,
		"v": {
			"mc": {
				"r": 21,
				"c": 3
			}
		}
	}, {
		"r": 21,
		"c": 6,
		"v": {
			"v": "Student 5",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Student 5",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 21,
		"c": 7,
		"v": {
			"v": 106,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "106",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 21,
				"c": 7,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 21,
		"c": 8,
		"v": {
			"mc": {
				"r": 21,
				"c": 7
			}
		}
	}, {
		"r": 21,
		"c": 9,
		"v": {
			"v": 82,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "82",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 21,
				"c": 9,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 21,
		"c": 10,
		"v": {
			"mc": {
				"r": 21,
				"c": 9
			}
		}
	}, {
		"r": 21,
		"c": 11,
		"v": {
			"v": 80,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "80",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"mc": {
				"r": 21,
				"c": 11,
				"rs": 1,
				"cs": 2
			}
		}
	}, {
		"r": 21,
		"c": 12,
		"v": {
			"mc": {
				"r": 21,
				"c": 11
			}
		}
	}, {
		"r": 21,
		"c": 13,
		"v": {
			"v": 268,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "268",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 22,
		"c": 0,
		"v": {
			"v": "Employee 1",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Employee 1",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 22,
		"c": 1,
		"v": {
			"v": 6,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "6",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 22,
		"c": 2,
		"v": {
			"v": 2,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2"
		}
	}, {
		"r": 22,
		"c": 3,
		"v": {
			"mc": {
				"r": 22,
				"c": 3,
				"rs": 3,
				"cs": 2
			},
			"f": "=STACKCOLUMNSPLINES(B23:C25)",
			"spl": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 60, 57, 58, 5, "#2ec7c9", "#2ec7c9"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 60, 36, 58, 20, "#fc5c5c", "#fc5c5c"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 60, 27, 58, 8, "#5ab1ef", "#5ab1ef"]
					},
					"3": {
						"id": 3,
						"type": "Rect",
						"args": [3, 0, 45, 58, 17, "#2ec7c9", "#2ec7c9"]
					},
					"4": {
						"id": 4,
						"type": "Rect",
						"args": [4, 0, 21, 58, 23, "#fc5c5c", "#fc5c5c"]
					},
					"5": {
						"id": 5,
						"type": "Rect",
						"args": [5, 0, 3, 58, 17, "#5ab1ef", "#5ab1ef"]
					}
				},
				"shapeseq": [0, 1, 2, 3, 4, 5],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 121,
				"pixelHeight": 63
			}
		}
	}, {
		"r": 22,
		"c": 4,
		"v": {
			"mc": {
				"r": 22,
				"c": 3
			}
		}
	}, {
		"r": 23,
		"c": 0,
		"v": {
			"v": "Employee 2",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Employee 2",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 23,
		"c": 1,
		"v": {
			"v": 8,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "8",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 23,
		"c": 2,
		"v": {
			"v": 7,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "7",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 23,
		"c": 3,
		"v": {
			"mc": {
				"r": 22,
				"c": 3
			}
		}
	}, {
		"r": 23,
		"c": 4,
		"v": {
			"mc": {
				"r": 22,
				"c": 3
			}
		}
	}, {
		"r": 23,
		"c": 5,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			}
		}
	}, {
		"r": 24,
		"c": 0,
		"v": {
			"v": "Employee 3",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Employee 3",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 24,
		"c": 1,
		"v": {
			"v": 6,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "6",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 24,
		"c": 2,
		"v": {
			"v": 3,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "3"
		}
	}, {
		"r": 24,
		"c": 3,
		"v": {
			"mc": {
				"r": 22,
				"c": 3
			}
		}
	}, {
		"r": 24,
		"c": 4,
		"v": {
			"mc": {
				"r": 22,
				"c": 3
			}
		}
	}, {
		"r": 25,
		"c": 0,
		"v": {
			"v": null,
			"m": "",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 25,
		"c": 1,
		"v": {
			"v": null,
			"m": "",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 25,
		"c": 2,
		"v": {
			"v": null,
			"m": "",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 25,
		"c": 3,
		"v": {
			"v": null,
			"m": "",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 25,
		"c": 4,
		"v": {
			"v": null,
			"m": "",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 27,
		"c": 0,
		"v": {
			"v": 42370,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-01"
		}
	}, {
		"r": 27,
		"c": 1,
		"v": {
			"v": 12,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "12",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 27,
		"c": 2,
		"v": {
			"f": "=DISCRETESPLINES(B28:B58,30)",
			"spl": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 240, 14, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 232, 55, 6, 30, "#fc5c5c", "#fc5c5c"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 224, 57, 6, 30, "#fc5c5c", "#fc5c5c"]
					},
					"3": {
						"id": 3,
						"type": "Rect",
						"args": [3, 216, 49, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"4": {
						"id": 4,
						"type": "Rect",
						"args": [4, 208, 68, 6, 30, "#fc5c5c", "#fc5c5c"]
					},
					"5": {
						"id": 5,
						"type": "Rect",
						"args": [5, 200, 71, 6, 30, "#fc5c5c", "#fc5c5c"]
					},
					"6": {
						"id": 6,
						"type": "Rect",
						"args": [6, 192, 45, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"7": {
						"id": 7,
						"type": "Rect",
						"args": [7, 184, 64, 6, 30, "#fc5c5c", "#fc5c5c"]
					},
					"8": {
						"id": 8,
						"type": "Rect",
						"args": [8, 176, 30, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"9": {
						"id": 9,
						"type": "Rect",
						"args": [9, 168, 32, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"10": {
						"id": 10,
						"type": "Rect",
						"args": [10, 160, 14, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"11": {
						"id": 11,
						"type": "Rect",
						"args": [11, 152, 12, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"12": {
						"id": 12,
						"type": "Rect",
						"args": [12, 144, 0, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"13": {
						"id": 13,
						"type": "Rect",
						"args": [13, 136, 65, 6, 30, "#fc5c5c", "#fc5c5c"]
					},
					"14": {
						"id": 14,
						"type": "Rect",
						"args": [14, 128, 7, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"15": {
						"id": 15,
						"type": "Rect",
						"args": [15, 120, 9, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"16": {
						"id": 16,
						"type": "Rect",
						"args": [16, 112, 54, 6, 30, "#fc5c5c", "#fc5c5c"]
					},
					"17": {
						"id": 17,
						"type": "Rect",
						"args": [17, 104, 3, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"18": {
						"id": 18,
						"type": "Rect",
						"args": [18, 96, 33, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"19": {
						"id": 19,
						"type": "Rect",
						"args": [19, 88, 1, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"20": {
						"id": 20,
						"type": "Rect",
						"args": [20, 80, 53, 6, 30, "#fc5c5c", "#fc5c5c"]
					},
					"21": {
						"id": 21,
						"type": "Rect",
						"args": [21, 72, 7, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"22": {
						"id": 22,
						"type": "Rect",
						"args": [22, 64, 25, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"23": {
						"id": 23,
						"type": "Rect",
						"args": [23, 56, 8, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"24": {
						"id": 24,
						"type": "Rect",
						"args": [24, 48, 59, 6, 30, "#fc5c5c", "#fc5c5c"]
					},
					"25": {
						"id": 25,
						"type": "Rect",
						"args": [25, 40, 22, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"26": {
						"id": 26,
						"type": "Rect",
						"args": [26, 32, 46, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"27": {
						"id": 27,
						"type": "Rect",
						"args": [27, 24, 60, 6, 30, "#fc5c5c", "#fc5c5c"]
					},
					"28": {
						"id": 28,
						"type": "Rect",
						"args": [28, 16, 32, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"29": {
						"id": 29,
						"type": "Rect",
						"args": [29, 8, 25, 6, 30, "#2ec7c9", "#2ec7c9"]
					},
					"30": {
						"id": 30,
						"type": "Rect",
						"args": [30, 0, 62, 6, 30, "#fc5c5c", "#fc5c5c"]
					}
				},
				"shapeseq": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 253,
				"pixelHeight": 101
			},
			"mc": {
				"r": 27,
				"c": 2,
				"rs": 1,
				"cs": 3
			}
		}
	}, {
		"r": 27,
		"c": 3,
		"v": {
			"mc": {
				"r": 27,
				"c": 2
			}
		}
	}, {
		"r": 27,
		"c": 4,
		"v": {
			"mc": {
				"r": 27,
				"c": 2
			}
		}
	}, {
		"r": 28,
		"c": 0,
		"v": {
			"v": 42371,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-02"
		}
	}, {
		"r": 28,
		"c": 1,
		"v": {
			"v": 64,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "64",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 29,
		"c": 0,
		"v": {
			"v": 42372,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-03"
		}
	}, {
		"r": 29,
		"c": 1,
		"v": {
			"v": 54,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "54",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 30,
		"c": 0,
		"v": {
			"v": 42373,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-04"
		}
	}, {
		"r": 30,
		"c": 1,
		"v": {
			"v": 15,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "15",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 31,
		"c": 0,
		"v": {
			"v": 42374,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-05"
		}
	}, {
		"r": 31,
		"c": 1,
		"v": {
			"v": 35,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "35",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 31,
		"c": 5,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			}
		}
	}, {
		"r": 32,
		"c": 0,
		"v": {
			"v": 42375,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-06"
		}
	}, {
		"r": 32,
		"c": 1,
		"v": {
			"v": 67,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "67",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 32,
		"c": 2,
		"v": {
			"f": "=BARSPLINES(B22:B25)"
		}
	}, {
		"r": 32,
		"c": 5,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			}
		}
	}, {
		"r": 33,
		"c": 0,
		"v": {
			"v": 42376,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-07"
		}
	}, {
		"r": 33,
		"c": 1,
		"v": {
			"v": 16,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "16",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 33,
		"c": 2,
		"v": {
			"f": "=STACKBARSPLINES(B22:B25)"
		}
	}, {
		"r": 33,
		"c": 5,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			}
		}
	}, {
		"r": 34,
		"c": 0,
		"v": {
			"v": 42377,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-08"
		}
	}, {
		"r": 34,
		"c": 1,
		"v": {
			"v": 87,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "87",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 34,
		"c": 2,
		"v": {
			"f": "=DISCRETESPLINES(B22:B25)"
		}
	}, {
		"r": 34,
		"c": 5,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			}
		}
	}, {
		"r": 34,
		"c": 7,
		"v": {
			"ct": {
				"fa": "General",
				"t": "n"
			}
		}
	}, {
		"r": 35,
		"c": 0,
		"v": {
			"v": 42378,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-09"
		}
	}, {
		"r": 35,
		"c": 1,
		"v": {
			"v": 64,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "64",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 35,
		"c": 2,
		"v": {
			"f": "=TRISTATESPLINES(B22:B25)"
		}
	}, {
		"r": 36,
		"c": 0,
		"v": {
			"v": 42379,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-10"
		}
	}, {
		"r": 36,
		"c": 1,
		"v": {
			"v": 88,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "88",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 36,
		"c": 2,
		"v": {
			"ct": {
				"fa": "General",
				"t": "e"
			}
		}
	}, {
		"r": 37,
		"c": 0,
		"v": {
			"v": 42380,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-11"
		}
	}, {
		"r": 37,
		"c": 1,
		"v": {
			"v": 25,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "25",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 38,
		"c": 0,
		"v": {
			"v": 42381,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-12"
		}
	}, {
		"r": 38,
		"c": 1,
		"v": {
			"v": 96,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "96",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 39,
		"c": 0,
		"v": {
			"v": 42382,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-13"
		}
	}, {
		"r": 39,
		"c": 1,
		"v": {
			"v": 53,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "53",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 40,
		"c": 0,
		"v": {
			"v": 42383,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-14"
		}
	}, {
		"r": 40,
		"c": 1,
		"v": {
			"v": 94,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "94",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 41,
		"c": 0,
		"v": {
			"v": 42384,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-15"
		}
	}, {
		"r": 41,
		"c": 1,
		"v": {
			"v": 23,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "23",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 42,
		"c": 0,
		"v": {
			"v": 42385,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-16"
		}
	}, {
		"r": 42,
		"c": 1,
		"v": {
			"v": 85,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "85",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 43,
		"c": 0,
		"v": {
			"v": 42386,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-17"
		}
	}, {
		"r": 43,
		"c": 1,
		"v": {
			"v": 89,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "89",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 44,
		"c": 0,
		"v": {
			"v": 42387,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-18"
		}
	}, {
		"r": 44,
		"c": 1,
		"v": {
			"v": 8,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "8",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 45,
		"c": 0,
		"v": {
			"v": 42388,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-19"
		}
	}, {
		"r": 45,
		"c": 1,
		"v": {
			"v": 98,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "98",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 46,
		"c": 0,
		"v": {
			"v": 42389,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-20"
		}
	}, {
		"r": 46,
		"c": 1,
		"v": {
			"v": 82,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "82",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 47,
		"c": 0,
		"v": {
			"v": 42390,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-21"
		}
	}, {
		"r": 47,
		"c": 1,
		"v": {
			"v": 79,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "79",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 48,
		"c": 0,
		"v": {
			"v": 42391,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-22"
		}
	}, {
		"r": 48,
		"c": 1,
		"v": {
			"v": 54,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "54",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 49,
		"c": 0,
		"v": {
			"v": 42392,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-23"
		}
	}, {
		"r": 49,
		"c": 1,
		"v": {
			"v": 56,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "56",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 50,
		"c": 0,
		"v": {
			"v": 42393,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-24"
		}
	}, {
		"r": 50,
		"c": 1,
		"v": {
			"v": 10,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "10",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 51,
		"c": 0,
		"v": {
			"v": 42394,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-25"
		}
	}, {
		"r": 51,
		"c": 1,
		"v": {
			"v": 36,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "36",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 52,
		"c": 0,
		"v": {
			"v": 42395,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-26"
		}
	}, {
		"r": 52,
		"c": 1,
		"v": {
			"v": 0,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "0",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 53,
		"c": 0,
		"v": {
			"v": 42396,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-27"
		}
	}, {
		"r": 53,
		"c": 1,
		"v": {
			"v": 4,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "4",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 54,
		"c": 0,
		"v": {
			"v": 42397,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-28"
		}
	}, {
		"r": 54,
		"c": 1,
		"v": {
			"v": 31,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "31",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 55,
		"c": 0,
		"v": {
			"v": 42398,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-29"
		}
	}, {
		"r": 55,
		"c": 1,
		"v": {
			"v": 19,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "19",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 56,
		"c": 0,
		"v": {
			"v": 42399,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-30"
		}
	}, {
		"r": 56,
		"c": 1,
		"v": {
			"v": 22,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "22",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 57,
		"c": 0,
		"v": {
			"v": 42400,
			"ct": {
				"fa": "yyyy-MM-dd",
				"t": "d"
			},
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1,
			"m": "2016-01-31"
		}
	}, {
		"r": 57,
		"c": 1,
		"v": {
			"v": 78,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "78",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}],
	"ch_width": 1524,
	"rh_height": 1571,
	"luckysheet_select_save": [{
		"left": 504,
		"width": 73,
		"top": 746,
		"height": 20,
		"left_move": 504,
		"width_move": 73,
		"top_move": 746,
		"height_move": 20,
		"row": [29, 29],
		"column": [6, 6],
		"row_focus": 29,
		"column_focus": 6
	}],
	"luckysheet_selection_range": [],
	"scrollLeft": 0,
	"scrollTop": 562,
	"calcChain": [{
		"r": 2,
		"c": 2,
		"index": "4",
		"func": [true, "", "=LINESPLINES(B3:B5,'pink',4,'avg','yellow','red','green',3)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Shape",
						"args": [0, [
							[0, 21],
							[0, 21],
							[80, 54],
							[159, 3]
						], "pink", null, 4]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 0, null, 159, null, null, "yellow"]
					},
					"2": {
						"id": 2,
						"type": "Circle",
						"args": [2, 80, 54, 3, null, "green", null]
					},
					"3": {
						"id": 3,
						"type": "Circle",
						"args": [3, 159, 3, 3, null, "red", null]
					}
				},
				"shapeseq": [0, 1, 2, 3],
				"offsetX": 0,
				"offsetY": 5,
				"pixelWidth": 162,
				"pixelHeight": 58
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 2,
		"c": 4,
		"index": "4",
		"func": [true, "", "=COLUMNSPLINES(B3:B5,35,'red','green','auto','brown')", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 108, 1, 18, 29, "red", "red"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 54, 31, 18, 21, "green", "green"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 0, 20, 18, 10, "brown", "brown"]
					}
				},
				"shapeseq": [0, 1, 2],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 164,
				"pixelHeight": 63
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 8,
		"c": 4,
		"index": 4,
		"func": [true, 0.2631578947368421, "=B9/SUM(B9:B11)"],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 9,
		"c": 4,
		"index": 4,
		"func": [true, 0.5789473684210527, "=B10/SUM(B9:B11)"],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 10,
		"c": 4,
		"index": 4,
		"func": [true, 0.15789473684210525, "=B11/SUM(B9:B11)"],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 8,
		"c": 2,
		"index": 4,
		"func": [true, "", "=PIESPLINES(B9:B11)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "PieSlice",
						"args": [0, 31, 31, 31, 5.291103416572283, 6.283185307179586, null, "#5ab1ef"]
					},
					"1": {
						"id": 1,
						"type": "PieSlice",
						"args": [1, 31, 31, 31, 1.6534698176788385, 5.291103416572283, null, "#fc5c5c"]
					},
					"2": {
						"id": 2,
						"type": "PieSlice",
						"args": [2, 31, 31, 31, 0, 1.6534698176788385, null, "#2ec7c9"]
					}
				},
				"shapeseq": [0, 1, 2],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 162,
				"pixelHeight": 63
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 14,
		"c": 2,
		"index": 4,
		"func": [true, "", "=AREASPLINES(B15:B18)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Shape",
						"args": [0, [
							[0, 87],
							[0, 61],
							[84, 0],
							[169, 87],
							[253, 35],
							[253, 87]
						], "#CCF3F4", "#CCF3F4", null]
					},
					"1": {
						"id": 1,
						"type": "Shape",
						"args": [1, [
							[0, 61],
							[0, 61],
							[84, 0],
							[169, 87],
							[253, 35]
						], "#2ec7c9", null, 1]
					}
				},
				"shapeseq": [0, 1],
				"offsetX": 0,
				"offsetY": 2,
				"pixelWidth": 253,
				"pixelHeight": 88
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 32,
		"c": 2,
		"index": 4,
		"func": [true, "", "=BARSPLINES(B22:B25)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 0, 15, 129, 3, "#fc5c5c", "#fc5c5c"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 0, 10, 129, 3, "#fc5c5c", "#fc5c5c"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 0, 5, 129, 3, "#fc5c5c", "#fc5c5c"]
					},
					"3": {
						"id": 3,
						"type": "Rect",
						"args": [3, 0, 0, 129, 3, "#fc5c5c", "#fc5c5c"]
					}
				},
				"shapeseq": [0, 1, 2, 3],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 131,
				"pixelHeight": 20
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 33,
		"c": 2,
		"index": 4,
		"func": [true, "", "=STACKBARSPLINES(B22:B25)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 0, 0, 129, 18, "#2ec7c9", "#2ec7c9"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 130, 0, 129, 18, "#fc5c5c", "#fc5c5c"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 260, 0, 129, 18, "#5ab1ef", "#5ab1ef"]
					},
					"3": {
						"id": 3,
						"type": "Rect",
						"args": [3, 390, 0, 129, 18, "#ffb980", "#ffb980"]
					}
				},
				"shapeseq": [0, 1, 2, 3],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 131,
				"pixelHeight": 20
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 34,
		"c": 2,
		"index": 4,
		"func": [true, "", "=DISCRETESPLINES(B22:B25)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 96, null, 30, 6, "#2ec7c9", "#2ec7c9"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 64, null, 30, 6, "#2ec7c9", "#2ec7c9"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 32, null, 30, 6, "#2ec7c9", "#2ec7c9"]
					},
					"3": {
						"id": 3,
						"type": "Rect",
						"args": [3, 0, null, 30, 6, "#2ec7c9", "#2ec7c9"]
					}
				},
				"shapeseq": [0, 1, 2, 3],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 131,
				"pixelHeight": 20
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 35,
		"c": 2,
		"index": 4,
		"func": [true, "", "=TRISTATESPLINES(B22:B25)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 15, 0, 3, 8, "#fc5c5c", "#fc5c5c"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 10, 0, 3, 8, "#fc5c5c", "#fc5c5c"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 5, 0, 3, 8, "#fc5c5c", "#fc5c5c"]
					},
					"3": {
						"id": 3,
						"type": "Rect",
						"args": [3, 0, 9, 3, 1, "#999", "#999"]
					}
				},
				"shapeseq": [0, 1, 2, 3],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 131,
				"pixelHeight": 20
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 2,
		"c": 12,
		"index": 4,
		"func": [true, "", "=STACKBARSPLINES(K3:L3)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 0, 10, 107, 8, "#fc5c5c", "#fc5c5c"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 0, 0, 145, 8, "#fc5c5c", "#fc5c5c"]
					}
				},
				"shapeseq": [0, 1],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 147,
				"pixelHeight": 21
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 27,
		"c": 2,
		"index": 4,
		"func": [true, "", "=DISCRETESPLINES(B28:B58,30)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 120, 3, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 116, 10, 2, 6, "#fc5c5c", "#fc5c5c"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 112, 10, 2, 6, "#fc5c5c", "#fc5c5c"]
					},
					"3": {
						"id": 3,
						"type": "Rect",
						"args": [3, 108, 9, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"4": {
						"id": 4,
						"type": "Rect",
						"args": [4, 104, 12, 2, 6, "#fc5c5c", "#fc5c5c"]
					},
					"5": {
						"id": 5,
						"type": "Rect",
						"args": [5, 100, 13, 2, 6, "#fc5c5c", "#fc5c5c"]
					},
					"6": {
						"id": 6,
						"type": "Rect",
						"args": [6, 96, 8, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"7": {
						"id": 7,
						"type": "Rect",
						"args": [7, 92, 12, 2, 6, "#fc5c5c", "#fc5c5c"]
					},
					"8": {
						"id": 8,
						"type": "Rect",
						"args": [8, 88, 6, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"9": {
						"id": 9,
						"type": "Rect",
						"args": [9, 84, 6, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"10": {
						"id": 10,
						"type": "Rect",
						"args": [10, 80, 3, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"11": {
						"id": 11,
						"type": "Rect",
						"args": [11, 76, 2, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"12": {
						"id": 12,
						"type": "Rect",
						"args": [12, 72, 0, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"13": {
						"id": 13,
						"type": "Rect",
						"args": [13, 68, 12, 2, 6, "#fc5c5c", "#fc5c5c"]
					},
					"14": {
						"id": 14,
						"type": "Rect",
						"args": [14, 64, 1, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"15": {
						"id": 15,
						"type": "Rect",
						"args": [15, 60, 2, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"16": {
						"id": 16,
						"type": "Rect",
						"args": [16, 56, 10, 2, 6, "#fc5c5c", "#fc5c5c"]
					},
					"17": {
						"id": 17,
						"type": "Rect",
						"args": [17, 52, 1, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"18": {
						"id": 18,
						"type": "Rect",
						"args": [18, 48, 6, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"19": {
						"id": 19,
						"type": "Rect",
						"args": [19, 44, 0, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"20": {
						"id": 20,
						"type": "Rect",
						"args": [20, 40, 10, 2, 6, "#fc5c5c", "#fc5c5c"]
					},
					"21": {
						"id": 21,
						"type": "Rect",
						"args": [21, 36, 1, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"22": {
						"id": 22,
						"type": "Rect",
						"args": [22, 32, 5, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"23": {
						"id": 23,
						"type": "Rect",
						"args": [23, 28, 1, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"24": {
						"id": 24,
						"type": "Rect",
						"args": [24, 24, 11, 2, 6, "#fc5c5c", "#fc5c5c"]
					},
					"25": {
						"id": 25,
						"type": "Rect",
						"args": [25, 20, 4, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"26": {
						"id": 26,
						"type": "Rect",
						"args": [26, 16, 8, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"27": {
						"id": 27,
						"type": "Rect",
						"args": [27, 12, 11, 2, 6, "#fc5c5c", "#fc5c5c"]
					},
					"28": {
						"id": 28,
						"type": "Rect",
						"args": [28, 8, 6, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"29": {
						"id": 29,
						"type": "Rect",
						"args": [29, 4, 5, 2, 6, "#2ec7c9", "#2ec7c9"]
					},
					"30": {
						"id": 30,
						"type": "Rect",
						"args": [30, 0, 11, 2, 6, "#fc5c5c", "#fc5c5c"]
					}
				},
				"shapeseq": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 131,
				"pixelHeight": 19
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 3,
		"c": 12,
		"index": 4,
		"func": [true, "", "=STACKBARSPLINES(K4:L4)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 0, 10, 47, 8, "#fc5c5c", "#fc5c5c"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 0, 0, 145, 8, "#fc5c5c", "#fc5c5c"]
					}
				},
				"shapeseq": [0, 1],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 147,
				"pixelHeight": 21
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 4,
		"c": 12,
		"index": 4,
		"func": [true, "", "=STACKBARSPLINES(K5:L5)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 0, 10, 125, 8, "#fc5c5c", "#fc5c5c"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 0, 0, 145, 8, "#fc5c5c", "#fc5c5c"]
					}
				},
				"shapeseq": [0, 1],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 147,
				"pixelHeight": 21
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 8,
		"c": 11,
		"index": 4,
		"func": [true, "", "=BARSPLINES(J9:J11)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 56, 42, 53, 19, "#97b552", "#97b552"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 110, 21, 108, 19, "#fc5c5c", "#fc5c5c"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 89, 0, 20, 19, "#97b552", "#97b552"]
					}
				},
				"shapeseq": [0, 1, 2],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 221,
				"pixelHeight": 63
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 15,
		"c": 7,
		"index": 4,
		"func": [true, "", "=TRISTATESPLINES(H18:N22,10)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 476, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 462, 23, 3, 1, "#999", "#999"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 448, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"3": {
						"id": 3,
						"type": "Rect",
						"args": [3, 434, 23, 3, 1, "#999", "#999"]
					},
					"4": {
						"id": 4,
						"type": "Rect",
						"args": [4, 420, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"5": {
						"id": 5,
						"type": "Rect",
						"args": [5, 406, 23, 3, 1, "#999", "#999"]
					},
					"6": {
						"id": 6,
						"type": "Rect",
						"args": [6, 392, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"7": {
						"id": 7,
						"type": "Rect",
						"args": [7, 378, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"8": {
						"id": 8,
						"type": "Rect",
						"args": [8, 364, 23, 3, 1, "#999", "#999"]
					},
					"9": {
						"id": 9,
						"type": "Rect",
						"args": [9, 350, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"10": {
						"id": 10,
						"type": "Rect",
						"args": [10, 336, 23, 3, 1, "#999", "#999"]
					},
					"11": {
						"id": 11,
						"type": "Rect",
						"args": [11, 322, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"12": {
						"id": 12,
						"type": "Rect",
						"args": [12, 308, 23, 3, 1, "#999", "#999"]
					},
					"13": {
						"id": 13,
						"type": "Rect",
						"args": [13, 294, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"14": {
						"id": 14,
						"type": "Rect",
						"args": [14, 280, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"15": {
						"id": 15,
						"type": "Rect",
						"args": [15, 266, 23, 3, 1, "#999", "#999"]
					},
					"16": {
						"id": 16,
						"type": "Rect",
						"args": [16, 252, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"17": {
						"id": 17,
						"type": "Rect",
						"args": [17, 238, 23, 3, 1, "#999", "#999"]
					},
					"18": {
						"id": 18,
						"type": "Rect",
						"args": [18, 224, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"19": {
						"id": 19,
						"type": "Rect",
						"args": [19, 210, 23, 3, 1, "#999", "#999"]
					},
					"20": {
						"id": 20,
						"type": "Rect",
						"args": [20, 196, 24, 3, 22, "#97b552", "#97b552"]
					},
					"21": {
						"id": 21,
						"type": "Rect",
						"args": [21, 182, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"22": {
						"id": 22,
						"type": "Rect",
						"args": [22, 168, 23, 3, 1, "#999", "#999"]
					},
					"23": {
						"id": 23,
						"type": "Rect",
						"args": [23, 154, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"24": {
						"id": 24,
						"type": "Rect",
						"args": [24, 140, 23, 3, 1, "#999", "#999"]
					},
					"25": {
						"id": 25,
						"type": "Rect",
						"args": [25, 126, 24, 3, 22, "#97b552", "#97b552"]
					},
					"26": {
						"id": 26,
						"type": "Rect",
						"args": [26, 112, 23, 3, 1, "#999", "#999"]
					},
					"27": {
						"id": 27,
						"type": "Rect",
						"args": [27, 98, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"28": {
						"id": 28,
						"type": "Rect",
						"args": [28, 84, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"29": {
						"id": 29,
						"type": "Rect",
						"args": [29, 70, 23, 3, 1, "#999", "#999"]
					},
					"30": {
						"id": 30,
						"type": "Rect",
						"args": [30, 56, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"31": {
						"id": 31,
						"type": "Rect",
						"args": [31, 42, 23, 3, 1, "#999", "#999"]
					},
					"32": {
						"id": 32,
						"type": "Rect",
						"args": [32, 28, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					},
					"33": {
						"id": 33,
						"type": "Rect",
						"args": [33, 14, 23, 3, 1, "#999", "#999"]
					},
					"34": {
						"id": 34,
						"type": "Rect",
						"args": [34, 0, 0, 3, 22, "#fc5c5c", "#fc5c5c"]
					}
				},
				"shapeseq": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 517,
				"pixelHeight": 48
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}, {
		"r": 22,
		"c": 3,
		"index": 4,
		"func": [true, "", "=STACKCOLUMNSPLINES(B23:C25)", {
			"type": "sparklines",
			"data": {
				"shapes": {
					"0": {
						"id": 0,
						"type": "Rect",
						"args": [0, 60, 57, 58, 5, "#2ec7c9", "#2ec7c9"]
					},
					"1": {
						"id": 1,
						"type": "Rect",
						"args": [1, 60, 36, 58, 20, "#fc5c5c", "#fc5c5c"]
					},
					"2": {
						"id": 2,
						"type": "Rect",
						"args": [2, 60, 27, 58, 8, "#5ab1ef", "#5ab1ef"]
					},
					"3": {
						"id": 3,
						"type": "Rect",
						"args": [3, 0, 45, 58, 17, "#2ec7c9", "#2ec7c9"]
					},
					"4": {
						"id": 4,
						"type": "Rect",
						"args": [4, 0, 21, 58, 23, "#fc5c5c", "#fc5c5c"]
					},
					"5": {
						"id": 5,
						"type": "Rect",
						"args": [5, 0, 3, 58, 17, "#5ab1ef", "#5ab1ef"]
					}
				},
				"shapeseq": [0, 1, 2, 3, 4, 5],
				"offsetX": 0,
				"offsetY": 0,
				"pixelWidth": 121,
				"pixelHeight": 63
			}
		}],
		"color": "w",
		"parent": null,
		"chidren": {},
		"times": 0
	}],
	"luckysheet_conditionformat_save": [],
	"filter_select": null,
	"filter": null,
	"luckysheet_alternateformat_save": []
}

// export default sheetSparkline;